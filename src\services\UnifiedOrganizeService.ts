import { localModelService } from './localModelService'
import { intelligence as intelligenceClient } from '../api/UnifiedAPIClient'

interface OrganizeOptions {
  modelId?: string
  includeExtensions?: string[]
  maxFiles?: number
}

interface VaultRegistry {
  version?: string
  vaultRoot?: string
  vaults?: Array<{ name?: string; path?: string }>
  lastScan?: string
  preferences?: Record<string, any>
}

export class UnifiedOrganizeService {
  private defaultExtensions: string[] = ['md', 'txt'] // keep initial scope simple

  async startOrganizeAllVaults(options: OrganizeOptions = {}): Promise<void> {
    const opts: Required<OrganizeOptions> = {
      modelId: options.modelId || 'ollama:gemma3:latest',
      includeExtensions: options.includeExtensions || this.defaultExtensions,
      maxFiles: options.maxFiles || 500
    }

    const registry = await this.getVaultRegistry()
    const vaultPaths = this.extractVaultPaths(registry)

    const allFiles: string[] = []

    // Scan all contexts in all vaults
    for (const vaultPath of vaultPaths) {
      try {
        const contextPaths = await this.getContextPaths(vaultPath)
        for (const contextPath of contextPaths) {
          const candidateFiles = await this.findCandidateFiles(contextPath, opts.includeExtensions)
          allFiles.push(...candidateFiles)
        }
      } catch (e) {
        console.warn('[ORGANIZE] Failed to scan vault:', vaultPath, e)
      }
    }

    const filesToProcess = allFiles.slice(0, opts.maxFiles)
    const total = filesToProcess.length
    const taskId = `organize_${Date.now()}`

    let processed = 0
    for (const filePath of filesToProcess) {
      try {
        // Emit progress
        processed += 1
        const percent = Math.round((processed / Math.max(1, total)) * 100)
        await this.emitProgress(taskId, percent, `Processing ${processed}/${total}`)

        const vaultPath = this.extractContextPath(filePath)
        if (!vaultPath) {
          console.warn('[ORGANIZE] No vaultPath for', filePath)
          continue
        }

        const content = await this.readTextContent(filePath)
        if (!content || content.trim().length === 0) {
          console.warn('[ORGANIZE] Empty content for', filePath)
          continue
        }

        // Generate markdown from local LLM using copied prompt
        const prompt = this.buildMarkdownPrompt(content)
        const response = await localModelService.sendMessage(opts.modelId, [{ role: 'user', content: prompt }])
        if (typeof response !== 'string' || response.trim().length === 0) {
          console.warn('[ORGANIZE] LLM empty response for', filePath)
          continue
        }

        // Save markdown sidecar via kernel (no legacy fallback)
        await intelligenceClient.write(filePath, vaultPath, { rawMarkdown: response })
      } catch (e) {
        console.error('[ORGANIZE] Failed to process file:', filePath, e)
      }
    }

    await this.emitProgress(taskId, 100, 'Organize complete')
  }

  private async getVaultRegistry(): Promise<VaultRegistry> {
    const result = await window.electronAPI.vault.getVaultRegistry()
    if (!result || !result.success) return {}
    // Handle new standardized response format
    return result.data || {}
  }

  private extractVaultPaths(registry: VaultRegistry): string[] {
    const paths = new Set<string>()
    if (registry?.vaults && Array.isArray(registry.vaults)) {
      for (const v of registry.vaults) {
        if (v?.path) paths.add(v.path)
        // Also accept vaultRoot + name
        if (registry.vaultRoot && v?.name) paths.add(`${registry.vaultRoot}\\${v.name}`)
      }
    }
    if (paths.size === 0 && registry?.vaultRoot) {
      // As a fallback, try scanning the root itself
      paths.add(registry.vaultRoot)
    }
    return Array.from(paths)
  }

  private async getContextPaths(vaultPath: string): Promise<string[]> {
    try {
      const res = await window.electronAPI.vault.scanContexts(vaultPath)
      if (res?.success && Array.isArray(res.contexts)) {
        return res.contexts.map((c: any) => c.path || `${vaultPath}\\${c.id}`)
      }
    } catch (e) {
      console.warn('[ORGANIZE] scanContexts failed for', vaultPath, e)
    }
    // If scanContexts not available, use vaultPath as one context
    return [vaultPath]
  }

  private async findCandidateFiles(contextPath: string, extensions: string[]): Promise<string[]> {
    // Look in context root and common folders like documents
    const dirs = [contextPath, `${contextPath}\\documents`]
    const results: string[] = []
    for (const dir of dirs) {
      try {
        const read = await window.electronAPI.vault.readDirectory(dir)
        if (read?.success && Array.isArray(read.items)) {
          for (const item of read.items) {
            if (item.isDirectory) continue
            const ext = (item.name.split('.').pop() || '').toLowerCase()
            if (extensions.includes(ext)) {
              results.push(item.path)
            }
          }
        }
      } catch (e) {
        // ignore missing dirs
      }
    }
    return results
  }

  private async readTextContent(filePath: string): Promise<string> {
    const base64OrString = await window.electronAPI.files.getFileContent(filePath)
    if (!base64OrString) return ''
    if (typeof base64OrString === 'string') {
      // Heuristic: if looks like base64 (non-UTF8), decode as base64 -> UTF-8
      try {
        // If contains non-base64 chars, assume already UTF-8
        if (/^[A-Za-z0-9+/=\r\n]+$/.test(base64OrString)) {
          return this.decodeBase64ToUTF8(base64OrString)
        }
        return base64OrString
      } catch {
        return base64OrString
      }
    }
    // If Buffer-like was returned
    try {
      const asString = (base64OrString as any).toString('base64')
      return this.decodeBase64ToUTF8(asString)
    } catch {
      return ''
    }
  }

  private decodeBase64ToUTF8(base64String: string): string {
    try {
      const binaryString = atob(base64String)
      const bytes = new Uint8Array(binaryString.length)
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i)
      }
      const decoder = new TextDecoder('utf-8')
      return decoder.decode(bytes)
    } catch {
      return ''
    }
  }

  // Copy-only: prompt to generate the markdown block
  private buildMarkdownPrompt(content: string): string {
    return `You are a precise extractor. Do NOT explain. Output exactly ONE fenced Markdown block, nothing else.

Task
- Read the document between DOC and DOC.
- Produce 10 key ideas as 1–4 word noun phrases (no verbs, no punctuation).
- Be tolerant: if the doc is short, output as many as you can (≥3).
- Use only these intents: topic, knowledge, connection, action, reference.

Output format (strict)
!-- FILE_INTEL:BEGIN --
## Key Ideas (1–4 words each)
- text=noun-phrase; score=98; intents=topic,knowledge; context=short hint; entities=Foo,Bar
- text=noun-phrase; score=95; intents=knowledge,action; context=short hint; entities=A,B,C
- text=noun-phrase; score=92; intents=topic; context=short hint; entities=
!-- FILE_INTEL:END --

Rules
- Exactly one block, no prose outside it.
- Each line starts with “- ” and uses key=value pairs separated by “; ”.
- text: 1–4 words, letters/numbers/spaces only, Title Case preferred, wrap in quotes.
- score: integer 0–100.
- intents: comma-separated from {topic,knowledge,connection,action,reference}.
- context: ≤10 words, quoted.
- entities: 0+ comma-separated tokens; leave empty if none.
- If you can’t find 10, output fewer but keep format.
- Do not include code fences inside the block.

DOC
${content.substring(0, 8000)}
DOC`
  }

  private extractContextPath(filePath: string): string | null {
    // Reuse preload util if available; otherwise infer by walking up until we find a '.intelligence' sibling or use top-level directory
    try {
      // Attempt to slice after a known vault segment
      const parts = filePath.replace(/\\/g, '/').split('/')
      const vaultIdx = parts.findIndex(p => p.toLowerCase().includes('vault'))
      if (vaultIdx >= 0) {
        const vault = parts.slice(0, vaultIdx + 2).join('\\')
        return vault
      }
    } catch {}
    return null
  }

  private async emitProgress(taskId: string, percent: number, message: string): Promise<void> {
    try {
      await (window.electronAPI as any).events.emit('task:progress', { taskId, percent, message })
    } catch {}
  }
}

export const unifiedOrganizeService = new UnifiedOrganizeService()
