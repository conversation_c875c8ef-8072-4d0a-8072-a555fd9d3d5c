/**
 * RelativeStorageService - Clean architecture for intelligence storage
 * 
 * ✅ DESIGN PRINCIPLES:
 * - No hardcoded paths
 * - Plugin-compatible architecture
 * - Relative path operations only
 * - Clean separation of concerns
 * - Comprehensive error handling
 */

import { BaseService, ServiceError, ServiceErrorCode } from './base'
import { joinLike } from '../utils/vaultPath'

// ===== INTERFACES =====

export interface StorageContext {
  contextId: string           // Unique context identifier (e.g., "getting-started")
  vaultId: string            // Vault identifier (e.g., "personal-vault")
  relativePath: string       // Path relative to vault root (e.g., "getting-started/documents")
  fileHash?: string          // Optional file hash for intelligence storage
}

export interface IntelligenceData {
  document_hash: string
  file_path: string
  analysis_timestamp: string
  labels?: string[]
  summary?: string
  key_points?: string[]
  [key: string]: any
}

export interface StorageResult<T = any> {
  success: boolean
  data?: T
  error?: string
}

// ===== MAIN SERVICE =====

export class RelativeStorageService extends BaseService {
  private readonly CONTEXT_DIR = '.context'
  private readonly FILES_DIR = 'files'
  private readonly VAULT_INTELLIGENCE_FILE = 'vault_intelligence.json'

  constructor() {
    super({ name: 'RelativeStorageService', autoInitialize: true })
  }

  /**
   * Initialize the service
   */
  protected async doInitialize(): Promise<void> {
    console.log('[RELATIVE-STORAGE] 🚀 Service initialized')
    // No specific initialization needed for this service
  }

  // ===== PUBLIC API =====

  /**
   * Store intelligence data for a file
   */
  async storeIntelligence(
    context: StorageContext, 
    intelligence: IntelligenceData
  ): Promise<StorageResult<boolean>> {
    const res = await this.executeOperation(
      'storeIntelligence',
      async () => {
        console.log('[RELATIVE-STORAGE] 💾 Storing intelligence for context:', context.contextId)

        // Generate file hash if not provided
        const fileHash = context.fileHash || this.generateFileHash(intelligence.file_path)

        // Build storage paths using relative operations
        const storagePath = this.buildIntelligenceStoragePath(context, fileHash)

        console.log('[RELATIVE-STORAGE] 💾 Storage path:', storagePath)
        console.log('[RELATIVE-STORAGE] 💾 No hardcoded paths detected ✅')

        // Ensure directory structure exists
        await this.ensureStorageDirectory(context)

        // Prepare intelligence data with metadata
        const enrichedIntelligence = {
          ...intelligence,
          storage_metadata: {
            context_id: context.contextId,
            vault_id: context.vaultId,
            file_hash: fileHash,
            stored_at: new Date().toISOString(),
            storage_version: '2.0'
          }
        }

        // Write intelligence file
        const writeResult = await this.writeIntelligenceFile(storagePath, enrichedIntelligence)

        if (!writeResult.success) {
          throw new ServiceError(
            ServiceErrorCode.STORAGE_ERROR,
            `Failed to store intelligence: ${writeResult.error}`,
            { serviceName: this.serviceName, operation: 'storeIntelligence' }
          )
        }

        console.log('[RELATIVE-STORAGE] ✅ Intelligence stored successfully')
        return true
      }
    )
    return { success: res.success, data: res.data, error: res.error?.message }
  }


  /**
   * Retrieve intelligence data for a file
   */
  async getIntelligence(context: StorageContext): Promise<StorageResult<IntelligenceData | null>> {
    const res = await this.executeOperation(
      'getIntelligence',
      async () => {
        console.log('[RELATIVE-STORAGE] 📖 Retrieving intelligence for context:', context.contextId)

        // Generate file hash if not provided
        const fileHash = context.fileHash || this.generateFileHash(context.relativePath)

        // Build storage path
        const storagePath = this.buildIntelligenceStoragePath(context, fileHash)

        console.log('[RELATIVE-STORAGE] 📖 Reading from path:', storagePath)

        // Read intelligence file
        const readResult = await this.readIntelligenceFile(storagePath)
        if (!readResult || !readResult.success) {
          console.log('[RELATIVE-STORAGE] ℹ️ No intelligence found for context:', context.contextId)
          return null
        }

        if (!readResult.success) {
          console.log('[RELATIVE-STORAGE] ℹ️ No intelligence found for context:', context.contextId)
          return null
        }

        console.log('[RELATIVE-STORAGE] ✅ Intelligence retrieved successfully')
        return readResult.data
      }
    )
    return { success: res.success, data: res.data as any, error: res.error?.message }
  }

  /**
   * Check if intelligence exists for a context
   */
  async hasIntelligence(context: StorageContext): Promise<StorageResult<boolean>> {
    const res = await this.executeOperation(
      'hasIntelligence',
      async () => {
        const fileHash = context.fileHash || this.generateFileHash(context.relativePath)
        const storagePath = this.buildIntelligenceStoragePath(context, fileHash)

        const existsResult = await this.checkFileExists(storagePath)
        return existsResult.success && existsResult.data
      }
    )
    return { success: res.success, data: (res.data as any) as boolean, error: res.error?.message }
  }

  /**
   * Delete intelligence for a context
   */
  async deleteIntelligence(context: StorageContext): Promise<StorageResult<boolean>> {
    const res = await this.executeOperation(
      'deleteIntelligence',
      async () => {
        const fileHash = context.fileHash || this.generateFileHash(context.relativePath)
        const storagePath = this.buildIntelligenceStoragePath(context, fileHash)

        // Check if file exists before attempting deletion
        const existsResult = await this.checkFileExists(storagePath)
        if (!existsResult.success || !existsResult.data) {
          console.log('[RELATIVE-STORAGE] ℹ️ Intelligence file does not exist, nothing to delete')
          return true // Consider this success
        }

        const deleteResult = await this.deleteFile(storagePath)
        return deleteResult.success
      }
    )
    return { success: res.success, data: (res.data as any) as boolean, error: res.error?.message }
  }

  /**
   * Clear all intelligence data for a vault (nuclear option for corrupted state)
   */
  async clearVaultIntelligence(vaultId: string, contextId?: string): Promise<StorageResult<boolean>> {
    const res = await this.executeOperation(
      'clearVaultIntelligence',
      async () => {
        console.log('[RELATIVE-STORAGE] 🗑️ Starting vault intelligence clearing')
        console.log('[RELATIVE-STORAGE] 🗑️ Vault ID:', vaultId)
        console.log('[RELATIVE-STORAGE] 🗑️ Context ID:', contextId || 'ALL CONTEXTS')

        try {
          if (contextId) {
            // Clear specific context
            await this.clearContextIntelligence(vaultId, contextId)
          } else {
            // Clear entire vault - this is more complex as we need to scan for contexts
            await this.clearEntireVault(vaultId)
          }

          console.log('[RELATIVE-STORAGE] ✅ Vault intelligence clearing completed successfully')
          return true

        } catch (error) {
          console.error('[RELATIVE-STORAGE] ❌ Error during vault clearing:', error)
          return false
        }
      }
    )
    return { success: res.success, data: (res.data as any) as boolean, error: res.error?.message }
  }

  /**
   * Clear intelligence for a specific context
   */
  async clearContextIntelligence(vaultId: string, contextId: string): Promise<void> {
    console.log('[RELATIVE-STORAGE] 🗑️ Clearing context intelligence:', vaultId, '/', contextId)

    // Build paths for context intelligence
    const contextPath = joinLike(vaultId, contextId, this.CONTEXT_DIR)
    const filesPath = joinLike(vaultId, contextId, this.CONTEXT_DIR, this.FILES_DIR)
    const vaultIntelligencePath = joinLike(vaultId, contextId, this.CONTEXT_DIR, this.VAULT_INTELLIGENCE_FILE)

    console.log('[RELATIVE-STORAGE] 🗑️ Target paths:')
    console.log('[RELATIVE-STORAGE] 🗑️   Context:', contextPath)
    console.log('[RELATIVE-STORAGE] 🗑️   Files:', filesPath)
    console.log('[RELATIVE-STORAGE] 🗑️   Vault intelligence:', vaultIntelligencePath)

    // Delete vault intelligence file
    console.log('[RELATIVE-STORAGE] 🗑️ Deleting vault intelligence file...')
    const vaultFileExists = await this.checkFileExists(vaultIntelligencePath)
    if (vaultFileExists.success && vaultFileExists.data) {
      await this.deleteFile(vaultIntelligencePath)
      console.log('[RELATIVE-STORAGE] ✅ Vault intelligence file deleted')
    } else {
      console.log('[RELATIVE-STORAGE] ℹ️ Vault intelligence file does not exist')
    }

    // Delete files directory
    console.log('[RELATIVE-STORAGE] 🗑️ Deleting files directory...')
    const dirExists = await this.checkDirectoryExists(filesPath)
    if (dirExists.success && dirExists.data) {
      await this.deleteDirectory(filesPath)
      console.log('[RELATIVE-STORAGE] ✅ Files directory deleted')
    } else {
      console.log('[RELATIVE-STORAGE] ℹ️ Files directory does not exist')
    }
  }

  /**
   * Clear entire vault intelligence (all contexts)
   */
  async clearEntireVault(vaultId: string): Promise<void> {
    console.log('[RELATIVE-STORAGE] 🗑️ Clearing entire vault intelligence:', vaultId)

    // For now, we'll implement a simple approach
    // In a full implementation, we'd scan for all contexts first
    const vaultPath = vaultId
    const contextPath = joinLike(vaultPath, this.CONTEXT_DIR)

    console.log('[RELATIVE-STORAGE] 🗑️ Clearing vault-level context at:', contextPath)

    // Delete vault-level intelligence
    const vaultIntelligencePath = joinLike(vaultPath, this.CONTEXT_DIR, this.VAULT_INTELLIGENCE_FILE)
    const filesPath = joinLike(vaultPath, this.CONTEXT_DIR, this.FILES_DIR)

    // Delete vault intelligence file
    const vaultFileExists = await this.checkFileExists(vaultIntelligencePath)
    if (vaultFileExists.success && vaultFileExists.data) {
      await this.deleteFile(vaultIntelligencePath)
      console.log('[RELATIVE-STORAGE] ✅ Vault-level intelligence file deleted')
    }

    // Delete files directory
    const dirExists = await this.checkDirectoryExists(filesPath)
    if (dirExists.success && dirExists.data) {
      await this.deleteDirectory(filesPath)
      console.log('[RELATIVE-STORAGE] ✅ Vault-level files directory deleted')
    }
  }

  // ===== PRIVATE HELPERS =====

  /**
   * Build intelligence storage path using relative operations
   */
  private buildIntelligenceStoragePath(context: StorageContext, fileHash: string): string {
    // Build path: vaultId/contextId/.context/files/fileHash.json
    return joinLike(
      context.vaultId,
      context.contextId,
      this.CONTEXT_DIR,
      this.FILES_DIR,
      `${fileHash}.json`
    )
  }

  /**
   * Resolve relative path to absolute path for vault API
   */
  private async resolveAbsolutePath(relativePath: string): Promise<string> {
    try {
      // Get vault registry to find the root path
      if (window.electronAPI?.vault?.getVaultRegistry) {
        const response = await window.electronAPI.vault.getVaultRegistry()
        const registryResult = response?.success ? response.data : null
        if (registryResult && registryResult.vaultRoot) {
          const vaultRoot = registryResult.vaultRoot
          // Join vault root with relative path
          const absolutePath = joinLike(vaultRoot, relativePath)
          console.log('[RELATIVE-STORAGE] 🔧 Resolved absolute path:', absolutePath)
          return absolutePath
        }
      }

      // Fallback: assume relative path is from current working directory
      console.log('[RELATIVE-STORAGE] ⚠️ Could not get vault root, using relative path as-is')
      return relativePath
    } catch (error) {
      console.error('[RELATIVE-STORAGE] ❌ Error resolving absolute path:', error)
      return relativePath
    }
  }

  /**
   * Generate consistent file hash from file path
   */
  private generateFileHash(filePath: string): string {
    // Simple hash generation - can be improved with crypto if needed
    let hash = 0
    for (let i = 0; i < filePath.length; i++) {
      const char = filePath.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(16).substring(0, 8)
  }

  /**
   * Ensure storage directory exists
   */
  private async ensureStorageDirectory(context: StorageContext): Promise<void> {
    const dirPath = joinLike(
      context.vaultId,
      context.contextId,
      this.CONTEXT_DIR,
      this.FILES_DIR
    )
    
    // Directory creation will be handled by the vault API
    console.log('[RELATIVE-STORAGE] 📁 Storage directory path:', dirPath)
  }

  /**
   * Write intelligence file using vault API
   */
  private async writeIntelligenceFile(
    storagePath: string,
    intelligence: IntelligenceData
  ): Promise<StorageResult<boolean>> {
    try {
      console.log('[RELATIVE-STORAGE] 💾 Writing intelligence file...')
      console.log('[RELATIVE-STORAGE] 💾 Relative storage path:', storagePath)

      // FIXED: Convert relative path to absolute path for vault API
      const absolutePath = await this.resolveAbsolutePath(storagePath)
      console.log('[RELATIVE-STORAGE] 💾 Absolute storage path:', absolutePath)

      console.log('[RELATIVE-STORAGE] 💾 Intelligence data preview:', {
        document_hash: intelligence.document_hash,
        file_path: intelligence.file_path,
        analysis_timestamp: intelligence.analysis_timestamp,
        labels_count: intelligence.labels?.length || 0,
        key_points_count: intelligence.key_points?.length || 0
      })

      if (!window.electronAPI?.vault?.writeFile) {
        console.error('[RELATIVE-STORAGE] ❌ Vault API not available')
        console.log('[RELATIVE-STORAGE] 🔍 Available APIs:', {
          electronAPI: !!window.electronAPI,
          vault: !!window.electronAPI?.vault,
          writeFile: !!window.electronAPI?.vault?.writeFile
        })
        throw new Error('Vault API not available')
      }

      const jsonContent = JSON.stringify(intelligence, null, 2)
      console.log('[RELATIVE-STORAGE] 💾 JSON content length:', jsonContent.length)
      console.log('[RELATIVE-STORAGE] 💾 Calling vault.writeFile with absolute path...')

      // FIXED: Use absolute path for vault API
      const result = await window.electronAPI.vault.writeFile(absolutePath, jsonContent)

      console.log('[RELATIVE-STORAGE] 💾 Vault writeFile result:', result)

      if (result.success) {
        console.log('[RELATIVE-STORAGE] ✅ Intelligence file written successfully')
      } else {
        console.error('[RELATIVE-STORAGE] ❌ Vault writeFile failed:', result.error)
      }

      return {
        success: result.success,
        data: result.success,
        error: result.error
      }
    } catch (error) {
      console.error('[RELATIVE-STORAGE] 💥 Error writing intelligence file:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Read intelligence file using vault API
   */
  private async readIntelligenceFile(storagePath: string): Promise<StorageResult<IntelligenceData>> {
    try {
      if (!window.electronAPI?.vault?.readFile) {
        throw new Error('Vault API not available')
      }
      
      const result = await window.electronAPI.vault.readFile(storagePath)
      
      if (!result.success) {
        return {
          success: false,
          error: result.error
        }
      }
      
      const intelligence = JSON.parse(result.content || '{}')
      return {
        success: true,
        data: intelligence
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Check if file exists using vault API
   */
  private async checkFileExists(storagePath: string): Promise<StorageResult<boolean>> {
    try {
      if (!window.electronAPI?.vault?.pathExists) {
        throw new Error('Vault API not available')
      }
      
      const result = await window.electronAPI.vault.pathExists(storagePath)
      return {
        success: true,
        data: result.exists
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Delete file using vault API
   */
  private async deleteFile(storagePath: string): Promise<StorageResult<boolean>> {
    try {
      if (!window.electronAPI?.vault?.removeFile) {
        throw new Error('Vault API not available')
      }

      const result = await window.electronAPI.vault.removeFile(storagePath)
      return {
        success: result.success,
        data: result.success,
        error: result.error
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Check if directory exists using vault API
   */
  private async checkDirectoryExists(directoryPath: string): Promise<StorageResult<boolean>> {
    try {
      if (!window.electronAPI?.vault?.pathExists) {
        throw new Error('Vault API not available')
      }

      const result = await window.electronAPI.vault.pathExists(directoryPath)
      return {
        success: true,
        data: result.exists
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Delete directory using vault API
   */
  private async deleteDirectory(directoryPath: string): Promise<StorageResult<boolean>> {
    try {
      if (!window.electronAPI?.vault?.removeDirectory) {
        throw new Error('Vault removeDirectory API not available')
      }

      const result = await window.electronAPI.vault.removeDirectory(directoryPath)
      return {
        success: result.success,
        data: result.success,
        error: result.error
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }
}

// ===== CONTEXT FACTORY =====

export class StorageContextFactory {
  /**
   * Create storage context from file path (migration helper)
   */
  static createFromFilePath(filePath: string): StorageContext {
    console.log('[CONTEXT-FACTORY] 🏭 Creating context from file path:', filePath)

    // Extract vault and context information from path
    const pathParts = filePath.split(/[/\\]/)

    // Find vault directory (contains 'vault' in name)
    const vaultIndex = pathParts.findIndex(part => part.includes('vault'))
    if (vaultIndex === -1) {
      throw new Error(`No vault directory found in path: ${filePath}`)
    }

    const vaultId = pathParts[vaultIndex]
    const contextId = pathParts[vaultIndex + 1] || 'default'

    // Build relative path from vault root
    const relativePath = pathParts.slice(vaultIndex + 1).join('/')

    const context: StorageContext = {
      contextId,
      vaultId,
      relativePath
    }

    console.log('[CONTEXT-FACTORY] ✅ Created context:', context)
    return context
  }

  /**
   * Create storage context with explicit parameters
   */
  static create(vaultId: string, contextId: string, relativePath: string): StorageContext {
    return {
      contextId,
      vaultId,
      relativePath
    }
  }

  /**
   * Create context with file hash for intelligence operations
   */
  static createWithHash(vaultId: string, contextId: string, relativePath: string, fileHash: string): StorageContext {
    return {
      contextId,
      vaultId,
      relativePath,
      fileHash
    }
  }
}

// Export singleton instance and factory
export const relativeStorageService = new RelativeStorageService()
export const storageContextFactory = StorageContextFactory
