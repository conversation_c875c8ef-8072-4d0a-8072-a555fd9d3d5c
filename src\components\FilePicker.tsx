import React, { useState, useEffect } from 'react'
import { createPortal } from 'react-dom'
import { FileRecord } from '../types'
import { sharedDropboxService } from '../services/sharedDropboxService'
import { contextVaultService } from '../services/contextVaultService'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { ICONS } from './Icons/index'
import { extractContextPath, joinLike } from '../utils/vaultPath'

// Type guard for metadata
interface FileMetadata {
  uploaded_via?: string
  context_id?: string
  is_directory?: boolean
  [key: string]: any
}

const isFileMetadata = (metadata: string | { [key: string]: any } | undefined): metadata is FileMetadata => {
  return typeof metadata === 'object' && metadata !== null
}

interface FilePickerProps {
  isOpen: boolean
  onClose: () => void
  onFileSelect: (files: FileRecord[]) => void
  mode?: 'unified' | 'files' | 'images' // Made optional with unified as default
}

const FilePicker: React.FC<FilePickerProps> = ({
  isOpen,
  onClose,
  onFileSelect,
  mode = 'unified' // Default to unified mode
}) => {
  const [files, setFiles] = useState<FileRecord[]>([])
  const [filteredFiles, setFilteredFiles] = useState<FileRecord[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedFiles, setSelectedFiles] = useState<Set<string>>(new Set())
  const [isLoading, setIsLoading] = useState(false)
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [currentPath, setCurrentPath] = useState<string>('')
  const [pathHistory, setPathHistory] = useState<string[]>([])
  const [autoParseEnabled, setAutoParseEnabled] = useState(true)
  const [fileTypeFilter, setFileTypeFilter] = useState<string>('all')
  const [sortBy, setSortBy] = useState<'name' | 'date' | 'size' | 'type'>('name')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc')


  useEffect(() => {
    if (isOpen) {
      // Only load files when actually needed
      loadAndFilterFiles()
    }
  }, [isOpen, searchQuery, currentPath]) // Removed mode dependency since we're unified now

  const loadAndFilterFiles = async () => {
    console.log('Loading files for FilePicker, mode:', mode, 'searchQuery:', searchQuery)
    setIsLoading(true)
    try {
      let results: FileRecord[] = []

      // Get current context selection
      const selectedContext = contextVaultService.getSelectedContext()

      if (selectedContext) {
        // Context vault selected - scan the specific vault's root folder
        console.log('Loading files from context vault root folder:', selectedContext.name)

        if (window.electronAPI?.vault) {
          try {
            // Get the context's root path and scan for files using vault utilities
            const basePath = selectedContext.path
            const scanPath = currentPath ? joinLike(basePath, currentPath) : basePath

            // Check if scanFolder method exists (requires app restart after adding new IPC handlers)
            if (typeof window.electronAPI.vault.scanFolder !== 'function') {
              console.warn('scanFolder method not available - please restart the Electron app to load new IPC handlers')
              return
            }

            const scanResult = await window.electronAPI.vault.scanFolder(scanPath)

            if (scanResult.success && scanResult.files) {
              // Convert vault files to FileRecord format
              results = scanResult.files.map((file: any) => {
                const getFileType = (filename: string, isDirectory: boolean) => {
                  if (isDirectory) return 'folder'
                  const ext = filename.toLowerCase().split('.').pop() || ''

                  // Enhanced file type detection
                  if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg', 'ico', 'tiff', 'tif'].includes(ext)) return 'image'
                  if (['pdf'].includes(ext)) return 'pdf'
                  if (['doc', 'docx', 'rtf', 'odt'].includes(ext)) return 'word'
                  if (['xls', 'xlsx', 'csv', 'ods'].includes(ext)) return 'excel'
                  if (['ppt', 'pptx', 'odp'].includes(ext)) return 'powerpoint'
                  if (['txt', 'md', 'markdown', 'rst', 'asciidoc'].includes(ext)) return 'text'
                  if (['js', 'ts', 'jsx', 'tsx', 'py', 'java', 'cpp', 'c', 'cs', 'php', 'rb', 'go', 'rs', 'swift', 'kt'].includes(ext)) return 'code'
                  if (['json', 'xml', 'yaml', 'yml', 'toml', 'ini', 'cfg', 'conf'].includes(ext)) return 'config'
                  if (['zip', 'rar', '7z', 'tar', 'gz', 'bz2', 'xz'].includes(ext)) return 'archive'
                  if (['mp4', 'avi', 'mkv', 'mov', 'wmv', 'flv', 'webm'].includes(ext)) return 'video'
                  if (['mp3', 'wav', 'flac', 'aac', 'ogg', 'm4a'].includes(ext)) return 'audio'
                  return 'document'
                }

                return {
                  id: file.name + '_' + file.lastModified,
                  filename: file.name,
                  filepath: file.path,
                  file_path: file.path, // Legacy compatibility
                  file_type: getFileType(file.name, file.isDirectory),
                  mime_type: undefined,
                  file_size: file.size || 0,
                  content_hash: '',
                  file_hash: '', // Legacy compatibility
                  created_at: file.lastModified || new Date().toISOString(),
                  updated_at: file.lastModified || new Date().toISOString(),
                  indexed_at: file.lastModified || new Date().toISOString(), // Legacy compatibility
                  extracted_content: undefined,
                  metadata: {
                    uploaded_via: 'context_vault',
                    context_id: selectedContext.id,
                    is_directory: file.isDirectory
                  }
                }
              })

              // Apply search filter
              if (searchQuery) {
                results = results.filter(file =>
                  file.filename.toLowerCase().includes(searchQuery.toLowerCase())
                )
              }

              // Apply file type filter
              if (fileTypeFilter !== 'all') {
                results = results.filter(file => file.file_type === fileTypeFilter)
              }

              // Apply mode filter for backward compatibility
              if (mode === 'images') {
                results = results.filter(file => file.file_type === 'image')
              }

              // Apply sorting
              results.sort((a, b) => {
                let comparison = 0
                switch (sortBy) {
                  case 'name':
                    comparison = a.filename.localeCompare(b.filename)
                    break
                  case 'date':
                    comparison = new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
                    break
                  case 'size':
                    comparison = a.file_size - b.file_size
                    break
                  case 'type':
                    comparison = a.file_type.localeCompare(b.file_type)
                    break
                }
                return sortOrder === 'asc' ? comparison : -comparison
              })
            }
          } catch (error) {
            console.error('Error scanning context vault folder:', error)
          }
        }
      } else {
        // No context selected - scan shared dropbox folder directly
        console.log('Loading files from shared dropbox folder')

        if (window.electronAPI?.vault) {
          try {
            // Get shared dropbox path
            const destination = await sharedDropboxService.getUploadDestination()
            console.log('Shared dropbox destination:', destination)

            if (destination.type === 'shared' && destination.path) {
              // Check if scanFolder method exists (requires app restart after adding new IPC handlers)
              if (typeof window.electronAPI.vault.scanFolder !== 'function') {
                console.warn('scanFolder method not available - please restart the Electron app to load new IPC handlers')
                return
              }

              // Handle current path for shared dropbox navigation using vault utilities
              const scanPath = currentPath ? joinLike(destination.path, currentPath) : destination.path

              // Scan the shared dropbox folder
              const scanResult = await window.electronAPI.vault.scanFolder(scanPath)

              if (scanResult.success && scanResult.files) {
                // Convert vault files to FileRecord format
                results = scanResult.files.map((file: any) => {
                  const getFileType = (filename: string, isDirectory: boolean) => {
                    if (isDirectory) return 'folder'
                    const ext = filename.toLowerCase().split('.').pop() || ''

                    // Enhanced file type detection (same as above)
                    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg', 'ico', 'tiff', 'tif'].includes(ext)) return 'image'
                    if (['pdf'].includes(ext)) return 'pdf'
                    if (['doc', 'docx', 'rtf', 'odt'].includes(ext)) return 'word'
                    if (['xls', 'xlsx', 'csv', 'ods'].includes(ext)) return 'excel'
                    if (['ppt', 'pptx', 'odp'].includes(ext)) return 'powerpoint'
                    if (['txt', 'md', 'markdown', 'rst', 'asciidoc'].includes(ext)) return 'text'
                    if (['js', 'ts', 'jsx', 'tsx', 'py', 'java', 'cpp', 'c', 'cs', 'php', 'rb', 'go', 'rs', 'swift', 'kt'].includes(ext)) return 'code'
                    if (['json', 'xml', 'yaml', 'yml', 'toml', 'ini', 'cfg', 'conf'].includes(ext)) return 'config'
                    if (['zip', 'rar', '7z', 'tar', 'gz', 'bz2', 'xz'].includes(ext)) return 'archive'
                    if (['mp4', 'avi', 'mkv', 'mov', 'wmv', 'flv', 'webm'].includes(ext)) return 'video'
                    if (['mp3', 'wav', 'flac', 'aac', 'ogg', 'm4a'].includes(ext)) return 'audio'
                    return 'document'
                  }

                  return {
                    id: file.name + '_' + file.lastModified,
                    filename: file.name,
                    filepath: file.path,
                    file_path: file.path, // Legacy compatibility
                    file_type: getFileType(file.name, file.isDirectory),
                    mime_type: undefined,
                    file_size: file.size || 0,
                    content_hash: '',
                    file_hash: '', // Legacy compatibility
                    created_at: file.lastModified || new Date().toISOString(),
                    updated_at: file.lastModified || new Date().toISOString(),
                    indexed_at: file.lastModified || new Date().toISOString(), // Legacy compatibility
                    extracted_content: undefined,
                    metadata: {
                      uploaded_via: 'shared_dropbox',
                      is_directory: file.isDirectory
                    }
                  }
                })

                // Apply search filter
                if (searchQuery) {
                  results = results.filter(file =>
                    file.filename.toLowerCase().includes(searchQuery.toLowerCase())
                  )
                }

                // Apply file type filter
                if (fileTypeFilter !== 'all') {
                  results = results.filter(file => file.file_type === fileTypeFilter)
                }

                // Apply mode filter for backward compatibility
                if (mode === 'images') {
                  results = results.filter(file => file.file_type === 'image')
                }

                // Apply sorting
                results.sort((a, b) => {
                  let comparison = 0
                  switch (sortBy) {
                    case 'name':
                      comparison = a.filename.localeCompare(b.filename)
                      break
                    case 'date':
                      comparison = new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
                      break
                    case 'size':
                      comparison = a.file_size - b.file_size
                      break
                    case 'type':
                      comparison = a.file_type.localeCompare(b.file_type)
                      break
                  }
                  return sortOrder === 'asc' ? comparison : -comparison
                })
              }
            }
          } catch (error) {
            console.error('Error scanning shared dropbox folder:', error)
          }
        }
      }

      console.log('Search results:', results.length, 'files')

      // Filter by mode (client-side for better UX)
      let filtered = results
      if (mode === 'images') {
        filtered = results.filter(file => file.file_type === 'image')
      } else {
        filtered = results.filter(file => file.file_type !== 'image')
      }
      console.log('Filtered results:', filtered.length, 'files')

      setFiles(results) // Keep original for reference
      setFilteredFiles(filtered)
    } catch (error) {
      console.error('Error loading files:', error)
      setFilteredFiles([])
    } finally {
      setIsLoading(false)
    }
  }



  const handleFileClick = (file: FileRecord) => {
    const metadata = isFileMetadata(file.metadata) ? file.metadata : {}
    if (metadata.is_directory || file.file_type === 'folder') {
      // Navigate into folder
      navigateToFolder(file.filename)
    } else {
      // Toggle file selection
      toggleFileSelection(file.id)

      // Auto-parse if enabled
      if (autoParseEnabled) {
        handleAutoParseFile(file)
      }
    }
  }

  const toggleFileSelection = (fileId: string) => {
    console.log('Toggling file selection for:', fileId)
    const newSelection = new Set(selectedFiles)
    if (newSelection.has(fileId)) {
      newSelection.delete(fileId)
      console.log('Deselected file:', fileId)
    } else {
      newSelection.add(fileId)
      console.log('Selected file:', fileId)
    }
    setSelectedFiles(newSelection)
    console.log('Total selected files:', newSelection.size)
  }

  const navigateToFolder = (folderName: string) => {
    const newPath = currentPath ? `${currentPath}/${folderName}` : folderName
    setPathHistory([...pathHistory, currentPath])
    setCurrentPath(newPath)
    setSelectedFiles(new Set()) // Clear selections when navigating
  }

  const navigateBack = () => {
    if (pathHistory.length > 0) {
      const previousPath = pathHistory[pathHistory.length - 1]
      setCurrentPath(previousPath)
      setPathHistory(pathHistory.slice(0, -1))
      setSelectedFiles(new Set()) // Clear selections when navigating
    }
  }

  const handleAutoParseFile = async (file: FileRecord) => {
    console.log('Auto-parsing file:', file.filename, 'type:', file.file_type)

    try {
      // Check if file needs processing based on type and extension
      const fileExtension = file.filename.toLowerCase().split('.').pop() || ''
      const needsProcessing = [
        'pdf', 'document', 'text', 'markdown', 'word', 'excel', 'powerpoint'
      ].includes(file.file_type) || [
        'pdf', 'doc', 'docx', 'txt', 'md', 'xls', 'xlsx', 'ppt', 'pptx'
      ].includes(fileExtension)

      console.log('File extension:', fileExtension, 'Needs processing:', needsProcessing)

      if (needsProcessing && window.electronAPI?.files) {
        console.log('Starting auto-parse for:', file.filename)

        // Use appropriate indexing method based on file source
        let result
        const metadata = isFileMetadata(file.metadata) ? file.metadata : {}
        const filePath = file.file_path || file.filepath

        if (!filePath) {
          console.warn('No file path available for auto-parse:', file.filename)
          return
        }

        if (metadata.uploaded_via === 'context_vault') {
          // For vault files, use vault-aware indexing
          const currentContext = contextVaultService.getSelectedContext()
          if (currentContext) {
            const vaultPath = currentContext.path
            // Use vault utilities for proper path handling
            const contextPath = extractContextPath(filePath)
            const relativePath = filePath.replace(vaultPath, '').replace(/^[/\\]/, '')
            result = await window.electronAPI.files.indexVaultFile(filePath, currentContext.name, relativePath, true)
          } else {
            console.warn('No context selected for vault file auto-parse')
            return
          }
        } else {
          // For legacy files, use regular indexing
          result = await window.electronAPI.files.indexFile(filePath, true)
        }

        if (result.success) {
          console.log('Auto-parse completed for:', file.filename)
          // File is now indexed and processed
        } else {
          console.warn('Auto-parse failed for:', file.filename, result.error)
        }
      } else {
        console.log('File type does not need processing:', file.file_type, 'extension:', fileExtension)
      }
    } catch (error) {
      console.error('Error during auto-parse:', error)
    }
  }

  const handleTestDirectParsing = async (file: FileRecord) => {
    try {
      console.log('Testing direct parsing for:', file.filename)
      const filePath = file.file_path || file.filepath
      if (!filePath) {
        console.error('No file path available for direct parsing test')
        return
      }
      const result = await window.electronAPI.files.testDirectParsing(filePath)
      console.log('Direct parsing test result:', result)

      if (result.success) {
        console.log('✅ Direct parsing successful!')
        console.log('File type:', result.fileType)
        console.log('Text length:', result.textLength)
        console.log('First 500 chars:', result.firstChars)
      } else {
        console.log('❌ Direct parsing failed:', result.error)
      }
    } catch (error) {
      console.error('Error during direct parsing test:', error)
    }
  }

  const handleSelectFiles = async () => {
    const selectedFileRecords = files.filter(file => selectedFiles.has(file.id))
    console.log('Selected files for attachment:', selectedFileRecords)

    // For vault files, we need to index them first to get proper database IDs
    const processedFiles = []

    for (const file of selectedFileRecords) {
      const metadata = isFileMetadata(file.metadata) ? file.metadata : {}
      const filePath = file.file_path || file.filepath

      if (!filePath) {
        console.warn('Skipping file without path:', file.filename)
        continue
      }

      if (metadata.uploaded_via === 'context_vault' || metadata.uploaded_via === 'shared_dropbox') {
        try {
          console.log('Indexing vault file for database:', file.filename)
          console.log('File path:', filePath)
          console.log('Auto-parse enabled:', autoParseEnabled)

          // For vault files, we need to use the new vault-aware indexing
          if (metadata.uploaded_via === 'context_vault') {
            // Get the current selected context from the context vault service
            const currentContext = contextVaultService.getSelectedContext()

            if (currentContext) {
              // Calculate relative path from vault root using vault utilities
              const vaultPath = currentContext.path
              const contextPath = extractContextPath(filePath)
              const relativePath = filePath.replace(vaultPath, '').replace(/^[/\\]/, '')

              console.log('Vault indexing:', {
                vaultName: currentContext.name,
                relativePath: relativePath,
                fullPath: filePath
              })

              // Use the new vault-aware indexing method
              const indexResult = await window.electronAPI.files.indexVaultFile(
                filePath,
                currentContext.name,
                relativePath,
                autoParseEnabled
              )

              console.log('Vault index result:', indexResult)

              if (indexResult && indexResult.success && indexResult.file) {
                // Use the indexed file with proper database ID
                processedFiles.push(indexResult.file)
                console.log('Vault file indexed successfully:', file.filename, 'ID:', indexResult.file.id)
              } else {
                console.warn('Failed to index vault file:', file.filename, 'Error:', indexResult.error)
                // Fallback to original file
                processedFiles.push(file)
              }
            } else {
              console.warn('No context selected for vault file, using legacy indexing:', file.filename)
              // Fallback to legacy indexing if no context is selected
              const indexResult = await window.electronAPI.files.indexFile(filePath, autoParseEnabled)

              if (indexResult && typeof indexResult === 'string') {
                const indexedFile = { ...file, id: indexResult }
                processedFiles.push(indexedFile)
              } else {
                processedFiles.push(file)
              }
            }
          } else {
            // For shared dropbox files, use legacy indexing
            const indexResult = await window.electronAPI.files.indexFile(filePath, autoParseEnabled)

            console.log('Legacy index result:', indexResult)

            if (indexResult && typeof indexResult === 'string') {
              // indexFile returns a string ID on success
              console.log('File indexed successfully with ID:', indexResult)
              // Create a new file record with the database ID
              const indexedFile = { ...file, id: indexResult }
              processedFiles.push(indexedFile)
            } else {
              console.warn('Failed to index file:', file.filename, 'Result:', indexResult)
              // Fallback to original file
              processedFiles.push(file)
            }
          }
        } catch (error) {
          console.error('Error indexing vault file:', file.filename, 'Error:', error)
          // Fallback to original file
          processedFiles.push(file)
        }
      } else {
        // Legacy file, use as-is
        processedFiles.push(file)
      }
    }

    console.log('Processed files for attachment:', processedFiles)
    onFileSelect(processedFiles)
    onClose()
  }

  const handleBrowseFiles = async () => {
    try {
      if (window.electronAPI?.files) {
        const result = await window.electronAPI.files.showOpenDialog({
          title: mode === 'images' ? 'Select Images' : 'Select Files',
          properties: ['openFile', 'multiSelections'],
          filters: mode === 'images' 
            ? [
                { name: 'Images', extensions: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'] },
                { name: 'All Files', extensions: ['*'] }
              ]
            : [
                { name: 'Documents', extensions: ['pdf', 'doc', 'docx', 'txt', 'md', 'rtf'] },
                { name: 'Spreadsheets', extensions: ['xls', 'xlsx', 'csv'] },
                { name: 'Presentations', extensions: ['ppt', 'pptx'] },
                { name: 'Images', extensions: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'] },
                { name: 'Code Files', extensions: ['js', 'ts', 'jsx', 'tsx', 'py', 'java', 'cpp', 'c', 'cs', 'php', 'rb', 'go'] },
                { name: 'All Files', extensions: ['*'] }
              ]
        })

        if (!result.canceled && result.filePaths.length > 0) {
          const fileIds = await Promise.all(
            result.filePaths.map((p: string) => window.electronAPI.files.indexFile(p))
          );
          const response = await window.electronAPI.files.getIndexedFiles();
          
          // Handle new modular API response format: { success: true, files: [...] }
          if (!response.success || !response.files) {
            console.warn('getIndexedFiles returned unsuccessful response:', response);
            return;
          }
          
          const allFiles = response.files;
          const selectedFiles = allFiles.filter(f => fileIds.includes(f.id));

          // Ensure the files are in the correct format for onFileSelect
          const fileRecords: FileRecord[] = selectedFiles.map(file => ({
            id: file.id,
            filename: file.filename,
            filepath: file.file_path || file.filepath || '',
            file_path: file.file_path, // Legacy compatibility
            file_type: file.file_type,
            mime_type: file.mime_type,
            file_size: file.file_size,
            content_hash: file.file_hash || file.content_hash || '',
            file_hash: file.file_hash, // Legacy compatibility
            created_at: file.indexed_at || file.created_at || new Date().toISOString(),
            updated_at: file.indexed_at || file.updated_at || new Date().toISOString(),
            indexed_at: file.indexed_at, // Legacy compatibility
            extracted_content: file.extracted_content,
            metadata: file.metadata,
          }));

          onFileSelect(fileRecords);
          onClose();
        }
      }
    } catch (error) {
      console.error('Error browsing files:', error)
    }
  }

  const getFileIcon = (fileType: string, isDirectory?: boolean) => {
    if (isDirectory || fileType === 'folder') {
      return <FontAwesomeIcon icon={ICONS.folder} className="h-4 w-4 text-yellow-500" />
    }

    switch (fileType) {
      case 'image':
        return <FontAwesomeIcon icon={ICONS.fileImage} className="h-4 w-4 text-blue-400" />
      case 'pdf':
        return <FontAwesomeIcon icon={ICONS.filePdf} className="h-4 w-4 text-red-400" />
      case 'word':
        return <FontAwesomeIcon icon={ICONS.fileWord} className="h-4 w-4 text-blue-600" />
      case 'excel':
        return <FontAwesomeIcon icon={ICONS.fileExcel} className="h-4 w-4 text-green-600" />
      case 'powerpoint':
        return <FontAwesomeIcon icon={ICONS.filePowerpoint} className="h-4 w-4 text-orange-600" />
      case 'text':
      case 'markdown':
        return <FontAwesomeIcon icon={ICONS.fileText} className="h-4 w-4 text-neutral-400" />
      case 'code':
        return <FontAwesomeIcon icon={ICONS.fileCode} className="h-4 w-4 text-purple-400" />
      case 'config':
        return <FontAwesomeIcon icon={ICONS.cog} className="h-4 w-4 text-gray-400" />
      case 'archive':
        return <FontAwesomeIcon icon={ICONS.fileZipper} className="h-4 w-4 text-amber-500" />
      case 'video':
        return <FontAwesomeIcon icon={ICONS.fileVideo} className="h-4 w-4 text-pink-400" />
      case 'audio':
        return <FontAwesomeIcon icon={ICONS.fileAudio} className="h-4 w-4 text-green-400" />
      default:
        return <FontAwesomeIcon icon={ICONS.file} className="h-4 w-4 text-neutral-400" />
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
  }

  if (!isOpen) return null

  return createPortal(
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-[60] p-4">
      <div className="bg-neutral-900 border border-neutral-800 rounded-xl shadow-2xl w-full max-w-6xl h-[85vh] flex flex-col overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-neutral-800 bg-neutral-900/50">
          <div>
            <h2 className="text-xl font-semibold text-white">
              {mode === 'images' ? 'Select Images' : mode === 'unified' ? 'Select Files' : 'Select Files'}
            </h2>
            <p className="text-sm text-neutral-400 mt-1">
              Choose files to attach to your message. Intelligence data will be loaded automatically.
            </p>
          </div>
          <button
            onClick={onClose}
            className="h-10 w-10 flex items-center justify-center rounded-lg hover:bg-neutral-800 transition-colors text-neutral-400 hover:text-white"
            title="Close file picker"
          >
            <FontAwesomeIcon icon={ICONS.xmark} className="h-5 w-5" />
          </button>
        </div>

        {/* Search, Browse, and Controls */}
        <div className="p-6 border-b border-neutral-800">
          {/* Navigation Breadcrumbs */}
          {currentPath && (
            <div className="flex items-center gap-2 mb-4 text-sm text-neutral-400">
              <button
                onClick={navigateBack}
                className="flex items-center gap-1 px-2 py-1 hover:bg-neutral-700 rounded transition-colors"
              >
                <FontAwesomeIcon icon={ICONS.arrowLeft} className="h-3 w-3" />
                Back
              </button>
              <span>/</span>
              <span className="text-neutral-300">{currentPath}</span>
            </div>
          )}

          <div className="flex gap-4 mb-4">
            <div className="flex-1 relative">
              <FontAwesomeIcon icon={ICONS.search} className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-neutral-500" />
              <input
                type="text"
                placeholder="Search files..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full bg-neutral-800 border border-neutral-700 rounded-lg pl-10 pr-4 py-2 text-sm focus:ring-2 focus:ring-indigo-500 focus:border-transparent outline-none"
              />
            </div>

            {/* Auto-Parse Toggle */}
            <div className="flex items-center gap-2">
              <label className="flex items-center gap-2 text-sm text-neutral-300 cursor-pointer">
                <input
                  type="checkbox"
                  checked={autoParseEnabled}
                  onChange={(e) => setAutoParseEnabled(e.target.checked)}
                  className="w-4 h-4 text-indigo-600 bg-neutral-800 border-neutral-600 rounded focus:ring-indigo-500 focus:ring-2"
                />
                <span>Auto-parse</span>
              </label>
              <div className="text-xs text-neutral-500" title="Automatically process files for intelligence extraction">
                <FontAwesomeIcon icon={ICONS.infoCircle} className="h-3 w-3" />
              </div>
            </div>
          </div>

          <div className="flex gap-4 mb-4">
            {/* File Type Filter */}
            <select
              value={fileTypeFilter}
              onChange={(e) => setFileTypeFilter(e.target.value)}
              className="bg-neutral-800 border border-neutral-700 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-indigo-500 focus:border-transparent outline-none"
            >
              <option value="all">All Types</option>
              <option value="image">Images</option>
              <option value="pdf">PDF</option>
              <option value="word">Word</option>
              <option value="excel">Excel</option>
              <option value="powerpoint">PowerPoint</option>
              <option value="text">Text</option>
              <option value="code">Code</option>
              <option value="config">Config</option>
              <option value="archive">Archives</option>
              <option value="video">Video</option>
              <option value="audio">Audio</option>
            </select>

            {/* Sort Options */}
            <select
              value={`${sortBy}-${sortOrder}`}
              onChange={(e) => {
                const [newSortBy, newSortOrder] = e.target.value.split('-') as [typeof sortBy, typeof sortOrder]
                setSortBy(newSortBy)
                setSortOrder(newSortOrder)
              }}
              className="bg-neutral-800 border border-neutral-700 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-indigo-500 focus:border-transparent outline-none"
            >
              <option value="name-asc">Name A-Z</option>
              <option value="name-desc">Name Z-A</option>
              <option value="date-desc">Newest First</option>
              <option value="date-asc">Oldest First</option>
              <option value="size-desc">Largest First</option>
              <option value="size-asc">Smallest First</option>
              <option value="type-asc">Type A-Z</option>
              <option value="type-desc">Type Z-A</option>
            </select>
          </div>

          <div className="flex gap-4">

            {/* View Mode Toggle */}
            <div className="flex border border-neutral-700 rounded-lg overflow-hidden">
              <button
                onClick={() => setViewMode('grid')}
                className={`px-3 py-2 text-sm transition-colors ${
                  viewMode === 'grid'
                    ? 'bg-indigo-500 text-white'
                    : 'bg-neutral-800 text-neutral-400 hover:bg-neutral-700'
                }`}
                title="Grid view"
              >
                <FontAwesomeIcon icon={ICONS.thLarge} className="h-4 w-4" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`px-3 py-2 text-sm transition-colors ${
                  viewMode === 'list'
                    ? 'bg-indigo-500 text-white'
                    : 'bg-neutral-800 text-neutral-400 hover:bg-neutral-700'
                }`}
                title="List view"
              >
                <FontAwesomeIcon icon={ICONS.list} className="h-4 w-4" />
              </button>
            </div>

            <button
              onClick={handleBrowseFiles}
              className="px-4 py-2 bg-neutral-700 hover:bg-neutral-600 text-neutral-200 rounded-lg text-sm font-medium transition-colors"
            >
              Browse
            </button>
            <button
              onClick={() => {
                console.log('Confirm button clicked, selected count:', selectedFiles.size)
                handleSelectFiles()
              }}
              disabled={selectedFiles.size === 0}
              className={`
                px-4 py-2 rounded-lg text-sm font-medium transition-colors
                ${selectedFiles.size > 0
                  ? 'bg-indigo-500 hover:bg-indigo-600 text-white shadow-lg shadow-indigo-500/25'
                  : 'bg-neutral-700 text-neutral-500 cursor-not-allowed'
                }
              `}
            >
              Confirm ({selectedFiles.size})
            </button>
          </div>

          {/* Auto-parse toggle and selection info */}
          <div className="flex items-center justify-between mt-4">
            <label className="flex items-center gap-2 text-sm text-neutral-400">
              <input
                type="checkbox"
                checked={autoParseEnabled}
                onChange={(e) => setAutoParseEnabled(e.target.checked)}
                className="w-4 h-4 text-indigo-500 bg-neutral-800 border-neutral-600 rounded focus:ring-indigo-500 focus:ring-2"
              />
              Auto-parse files when selected
            </label>

            {selectedFiles.size > 0 && (
              <div className="text-sm text-indigo-400">
                {selectedFiles.size} file{selectedFiles.size !== 1 ? 's' : ''} selected
              </div>
            )}
          </div>
        </div>

        {/* File List */}
        <div className="flex-1 overflow-y-auto p-6 bg-neutral-950/30">
          {isLoading ? (
            <div className="flex items-center justify-center h-40">
              <div className="flex flex-col items-center gap-3">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-500"></div>
                <div className="text-neutral-400">Loading files...</div>
              </div>
            </div>
          ) : filteredFiles.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-40 text-neutral-400">
              <FontAwesomeIcon icon={ICONS.folder} className="h-16 w-16 mb-4 opacity-50" />
              <p className="text-lg font-medium">No files found</p>
              <p className="text-sm mt-2 text-center max-w-md">
                Try adjusting your search terms or use the Browse button to select files from your system
              </p>
            </div>
          ) : viewMode === 'grid' ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {filteredFiles.map((file) => {
                const metadata = isFileMetadata(file.metadata) ? file.metadata : {}
                return (
                  <div
                    key={file.id}
                    onClick={() => handleFileClick(file)}
                    className={`
                      p-4 border-2 rounded-lg cursor-pointer transition-all relative
                      ${metadata.is_directory || file.file_type === 'folder'
                        ? 'border-yellow-500/50 hover:border-yellow-400 hover:bg-yellow-500/10'
                        : selectedFiles.has(file.id)
                          ? 'border-indigo-500 bg-indigo-500/20 shadow-lg shadow-indigo-500/25'
                          : 'border-neutral-700 hover:border-indigo-400 hover:bg-neutral-800/50'
                      }
                    `}
                  >
                    {selectedFiles.has(file.id) && !(metadata.is_directory || file.file_type === 'folder') && (
                      <div className="absolute top-2 right-2 flex gap-2">
                        <button
                          onClick={(e) => {
                            e.stopPropagation()
                            handleTestDirectParsing(file)
                          }}
                          className="px-2 py-1 text-xs bg-yellow-500/20 text-yellow-400 rounded hover:bg-yellow-500/30 transition-colors"
                          title="Test direct parsing"
                        >
                          Test
                        </button>
                        <div className="w-6 h-6 bg-indigo-500 rounded-full flex items-center justify-center">
                          <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        </div>
                      </div>
                    )}
                    <div className="flex items-start gap-3">
                      {getFileIcon(file.file_type, metadata.is_directory)}
                      <div className="flex-1 min-w-0">
                        <h3 className="font-medium text-sm truncate">{file.filename}</h3>
                        <p className="text-xs text-neutral-500 mt-1">
                          {metadata.is_directory || file.file_type === 'folder'
                            ? 'Folder'
                            : `${formatFileSize(file.file_size)} • ${file.file_type}`
                          }
                        </p>
                        <p className="text-xs text-neutral-600 mt-1">
                          {new Date(file.indexed_at || file.created_at).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          ) : (
            /* List View */
            <div className="space-y-1">
              {filteredFiles.map((file) => {
                const metadata = isFileMetadata(file.metadata) ? file.metadata : {}
                return (
                  <div
                    key={file.id}
                    onClick={() => handleFileClick(file)}
                    className={`
                      flex items-center gap-3 p-3 rounded-lg cursor-pointer transition-all
                      ${metadata.is_directory || file.file_type === 'folder'
                        ? 'hover:bg-yellow-500/10 border-l-2 border-l-yellow-500/50'
                        : selectedFiles.has(file.id)
                          ? 'bg-indigo-500/20 border-l-2 border-l-indigo-500'
                          : 'hover:bg-neutral-800/50 border-l-2 border-l-transparent'
                      }
                    `}
                  >
                    {getFileIcon(file.file_type, metadata.is_directory)}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2">
                        <h3 className="font-medium text-sm truncate">{file.filename}</h3>
                        {selectedFiles.has(file.id) && !(metadata.is_directory || file.file_type === 'folder') && (
                          <div className="w-4 h-4 bg-indigo-500 rounded-full flex items-center justify-center flex-shrink-0">
                            <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                          </div>
                        )}
                        {selectedFiles.has(file.id) && !(metadata.is_directory || file.file_type === 'folder') && (
                          <button
                            onClick={(e) => {
                              e.stopPropagation()
                              handleTestDirectParsing(file)
                            }}
                            className="px-2 py-1 text-xs bg-yellow-500/20 text-yellow-400 rounded hover:bg-yellow-500/30 transition-colors"
                            title="Test direct parsing"
                          >
                            Test Parse
                          </button>
                        )}
                      </div>
                      <div className="flex items-center gap-4 text-xs text-neutral-500 mt-1">
                        <span>
                          {metadata.is_directory || file.file_type === 'folder'
                            ? 'Folder'
                            : `${formatFileSize(file.file_size)} • ${file.file_type}`
                          }
                        </span>
                        <span>{new Date(file.indexed_at || file.created_at).toLocaleDateString()}</span>

                        {/* Intelligence Status Indicators */}
                        {!(metadata.is_directory || file.file_type === 'folder') && (
                          <>
                            {file.extracted_content && (
                              <div className="flex items-center gap-1 text-green-400">
                                <FontAwesomeIcon icon={ICONS.brain} className="h-3 w-3" />
                                <span>Processed</span>
                              </div>
                            )}
                            {autoParseEnabled && !file.extracted_content && (
                              <div className="flex items-center gap-1 text-yellow-400">
                                <FontAwesomeIcon icon={ICONS.clock} className="h-3 w-3" />
                                <span>Will process</span>
                              </div>
                            )}
                            {!autoParseEnabled && !file.extracted_content && (
                              <div className="flex items-center gap-1 text-neutral-500">
                                <FontAwesomeIcon icon={ICONS.fileText} className="h-3 w-3" />
                                <span>Raw file</span>
                              </div>
                            )}
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          )}
        </div>


      </div>
    </div>,
    document.body
  )
}

export default FilePicker
