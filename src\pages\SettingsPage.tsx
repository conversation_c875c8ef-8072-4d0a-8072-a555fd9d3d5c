import React, { useState, useEffect } from 'react'
import { useAppStore } from '../store'
import { <PERSON>Lef<PERSON>, Key, Settings, User, FileText, Folder, RefreshCw, Sliders } from '../components/Icons'
import { useNavigate } from 'react-router-dom'
import { vaultInitializer } from '../services/vaultInitializer'
import { vaultUIManager } from '../services/vaultUIManager'
import { contextVaultService } from '../services/contextVaultService'
import { sharedDropboxService } from '../services/sharedDropboxService'
import { IntelligenceTestLauncher } from '../components/IntelligenceTestLauncher'
import { IntelligenceAnalyticsDashboard } from '../components/IntelligenceAnalyticsDashboard'
import { PluginManager } from '../components/PluginManager'
import FileProcessingDiagnostics from '../components/FileProcessingDiagnostics'
import { cacheManager } from '../services/cacheManager'
import { FileViewerTestPage } from '../settings/fileviewer-test'

  // Read tab from URL (?tab=data)
  const params = new URLSearchParams(window.location.search)
  const initialTab = params.get('tab') || 'api'

const SettingsPage: React.FC = () => {
  const navigate = useNavigate()
  const { settings, updateSettings } = useAppStore()
  const [activeTab, setActiveTab] = useState(initialTab)
  const [localSettings, setLocalSettings] = useState(settings)
  const [isLoading, setIsLoading] = useState(false)
  const [testResult, setTestResult] = useState<string | null>(null)

  const [storageInfo, setStorageInfo] = useState<any>(null)

  // Vault-related state
  const [vaultRootPath, setVaultRootPath] = useState('')
  const [vaultInitializing, setVaultInitializing] = useState(false)
  const [vaultResult, setVaultResult] = useState<string | null>(null)
  const [showTemplateSelection, setShowTemplateSelection] = useState(false)

  // Portable mode state
  const [portableModeEnabled, setPortableModeEnabled] = useState(false)
  const [portableModeLoading, setPortableModeLoading] = useState(false)
  const [portableModeResult, setPortableModeResult] = useState<string | null>(null)

  // Diagnostics state
  const [showDiagnostics, setShowDiagnostics] = useState(false)
  
  // File Viewer Test state
  const [showFileViewerTest, setShowFileViewerTest] = useState(false)

  useEffect(() => {
    setLocalSettings(settings)
    loadStorageInfo()
    loadVaultRoot()
    loadPortableMode()
    loadSecuritySettings()
  }, [settings])



  const loadStorageInfo = async () => {
    try {
      if (window.electronAPI?.files) {
        const filesResponse = await window.electronAPI.files.getIndexedFiles()
        // Handle both direct array response and wrapped response
        const files = Array.isArray(filesResponse)
          ? filesResponse
          : (filesResponse?.files || filesResponse?.success ? filesResponse.files || [] : [])

        if (Array.isArray(files)) {
          const totalSize = files.reduce((sum, file) => sum + (file.file_size || 0), 0)
          setStorageInfo({
            totalFiles: files.length,
            totalSize: totalSize,
            fileTypes: files.reduce((acc, file) => {
              const fileType = file.file_type || 'unknown'
              acc[fileType] = (acc[fileType] || 0) + 1
              return acc
            }, {} as Record<string, number>)
          })
        } else {
          console.warn('Files response is not an array:', files)
          setStorageInfo({
            totalFiles: 0,
            totalSize: 0,
            fileTypes: {}
          })
        }
      }
    } catch (error) {
      console.error('Error loading storage info:', error)
    }
  }

  // Load vault root path
  const loadVaultRoot = async () => {
    try {
      console.log('=== SETTINGS PAGE VAULT ROOT DEBUG ===')

      // First try to load from database settings
      if (window.electronAPI?.settings) {
        const savedVaultRoot = await window.electronAPI.settings.get('vault-root-path')
        console.log('Raw vault root from database:', savedVaultRoot)
        console.log('Vault root type:', typeof savedVaultRoot)
        console.log('Vault root length:', savedVaultRoot ? savedVaultRoot.length : 'null/undefined')

        if (savedVaultRoot) {
          console.log('Using vault root from database:', savedVaultRoot)
          setVaultRootPath(savedVaultRoot)
          console.log('======================================')
          return
        }
      }

      // Fallback to registry file
      console.log('No database vault root, trying registry file...')
      const registry = await vaultUIManager.getVaultRegistry()
      if (registry) {
        console.log('Loaded vault root from registry:', registry.vaultRoot)
        console.log('Registry vault root type:', typeof registry.vaultRoot)
        setVaultRootPath(registry.vaultRoot)

        // Save to database for future use
        if (window.electronAPI?.settings) {
          await window.electronAPI.settings.set('vault-root-path', registry.vaultRoot)
          console.log('Saved registry vault root to database')
        }
      } else {
        console.log('No registry file found')
      }
      console.log('======================================')
    } catch (error) {
      console.error('Error loading vault root:', error)
    }
  }

  // Load portable mode setting
  const loadPortableMode = async () => {
    try {
      console.log('=== SETTINGS PAGE PORTABLE MODE DEBUG ===')
      
      if (window.electronAPI?.settings) {
        const savedPortableMode = await window.electronAPI.settings.get('portable-mode-enabled')
        console.log('Raw portable mode from database:', savedPortableMode)
        console.log('Portable mode type:', typeof savedPortableMode)
        
        if (savedPortableMode !== null && savedPortableMode !== undefined) {
          const enabled = savedPortableMode === true || savedPortableMode === 'true'
          console.log('Using portable mode from database:', enabled)
          setPortableModeEnabled(enabled)
        } else {
          console.log('No portable mode setting found, defaulting to false')
          setPortableModeEnabled(false)
        }
      } else {
        console.log('Electron API not available, defaulting portable mode to false')
        setPortableModeEnabled(false)
      }
      
      console.log('======================================')
    } catch (error) {
      console.error('Error loading portable mode setting:', error)
      setPortableModeEnabled(false)
    }
  }

  // Handle portable mode toggle
  const handlePortableModeToggle = async (enabled: boolean) => {
    try {
      console.log('=== PORTABLE MODE TOGGLE DEBUG ===')
      console.log('Setting portable mode to:', enabled)
      
      setPortableModeLoading(true)
      setPortableModeResult(null)
      
      if (window.electronAPI?.settings) {
        // Save the setting to database
        await window.electronAPI.settings.set('portable-mode-enabled', enabled)
        console.log('Portable mode setting saved to database')
        
        // Update local state
        setPortableModeEnabled(enabled)
        
        if (enabled) {
          // Enable portable mode
          if (window.electronAPI?.db?.connectPortableDB) {
            try {
              // Get the portable database path based on vault root
              const portablePath = `${vaultRootPath}/.chatlo-core/chatlo.db`
              console.log('Connecting to portable database at:', portablePath)
              
              const result = await window.electronAPI.db.connectPortableDB(portablePath)
              if (result && result.success) {
                setPortableModeResult('✅ Portable mode enabled successfully! Database is now stored on USB.')
                console.log('Portable mode enabled successfully')
              } else {
                throw new Error(result?.error || 'Failed to connect to portable database')
              }
            } catch (error) {
              console.error('Failed to connect to portable database:', error)
              // Revert the setting if connection fails
              await window.electronAPI.settings.set('portable-mode-enabled', false)
              setPortableModeEnabled(false)
              setPortableModeResult(`❌ Failed to enable portable mode: ${error instanceof Error ? error.message : 'Unknown error'}`)
            }
          } else {
            setPortableModeResult('✅ Portable mode enabled! Database will be stored on USB when you restart the app.')
          }
        } else {
          // Disable portable mode
          if (window.electronAPI?.db?.prepareForDisconnect) {
            try {
              console.log('Preparing to disconnect portable database...')
              const result = await window.electronAPI.db.prepareForDisconnect()
              if (result && result.success) {
                setPortableModeResult('✅ Portable mode disabled successfully! Database is now stored locally.')
                console.log('Portable mode disabled successfully')
              } else {
                throw new Error(result?.error || 'Failed to prepare for disconnect')
              }
            } catch (error) {
              console.error('Failed to prepare for disconnect:', error)
              setPortableModeResult(`❌ Failed to disable portable mode: ${error instanceof Error ? error.message : 'Unknown error'}`)
            }
          } else {
            setPortableModeResult('✅ Portable mode disabled! Database will be stored locally when you restart the app.')
          }
        }
      } else {
        throw new Error('Electron API not available')
      }
      
      console.log('======================================')
    } catch (error) {
      console.error('Error toggling portable mode:', error)
      setPortableModeResult(`❌ Error: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setPortableModeLoading(false)
    }
  }

  // Load security settings from database
  const loadSecuritySettings = async () => {
    try {
      if (window.electronAPI?.settings) {
        const securityLevel = await window.electronAPI.settings.get('securityLevel')
        const allowedVaultPatterns = await window.electronAPI.settings.get('allowedVaultPatterns')
        
        if (securityLevel || allowedVaultPatterns) {
          setLocalSettings(prev => ({
            ...prev,
            securityLevel: securityLevel || 'balanced',
            allowedVaultPatterns: allowedVaultPatterns || ''
          }))
        }
      }
    } catch (error) {
      console.error('Error loading security settings:', error)
    }
  }

  // Refresh services after vault root path change
  const refreshServicesAfterVaultChange = async () => {
    try {
      // Refresh the context vault service to load data from new location
      await contextVaultService.refreshVaults()
      console.log('Context vault service refreshed after vault path change')

      // Reinitialize shared dropbox service for new vault location
      await sharedDropboxService.initialize()
      console.log('Shared dropbox service reinitialized after vault path change')
    } catch (error) {
      console.error('Error refreshing services after vault change:', error)
    }
  }

  // Handle vault root selection
  const handleSelectVaultRoot = async () => {
    try {
      setVaultInitializing(true)
      if (window.electronAPI?.files) {
        const result = await window.electronAPI.files.showOpenDialog({
          title: 'Select Vault Root Folder',
          properties: ['openDirectory'],
          defaultPath: vaultRootPath || undefined
        })

        if (!result.canceled && result.filePaths.length > 0) {
          const newPath = result.filePaths[0]
          setVaultRootPath(newPath)

          // Save vault root path to database
          if (window.electronAPI?.settings) {
            await window.electronAPI.settings.set('vault-root-path', newPath)
            console.log('Vault root path saved to database:', newPath)
          }

          // Update main process FileSystemManager and reindex to avoid legacy path drift
          if (window.electronAPI?.files?.setVaultRootPath) {
            try {
              const resp = await window.electronAPI.files.setVaultRootPath(newPath)
              if ((resp as any)?.success === false) {
                console.warn('[SETTINGS] files.setVaultRootPath reported error:', (resp as any)?.error)
              }
            } catch (e) {
              console.warn('[SETTINGS] Failed to set vault root in FileSystemManager:', (e as Error)?.message)
            }
          }

          // Invalidate vault-related caches to force fresh scan
          try {
            await cacheManager.remove('vault_registry')
            await cacheManager.remove('vault_scan_result')
            await cacheManager.remove('vault_cards')
            console.log('[SETTINGS] Cleared vault caches after root change')
          } catch (e) {
            console.warn('[SETTINGS] Cache invalidation failed:', (e as Error)?.message)
          }

          // Notify app about vault root change
          try {
            await window.electronAPI.events?.emit('vault:rootChanged', { vaultRoot: newPath })
          } catch (e) {
            console.warn('[SETTINGS] Failed to emit vault:rootChanged event:', (e as Error)?.message)
          }

          // Check if this is an existing vault (has registry file at the NEW location)
          // CRITICAL: Registry is stored at .chatlo/vault-registry.json, not vault-registry.json
          const registryPath = `${newPath}/.chatlo/vault-registry.json`
          console.log('🔍 [VAULT-CONTINUITY] Checking for existing registry at:', registryPath)

          const registryExists = await window.electronAPI?.vault?.pathExists(registryPath)
          console.log('🔍 [VAULT-CONTINUITY] Registry exists:', registryExists?.exists)

          if (registryExists?.exists) {
            // CRITICAL: Existing vault detected - preserve vault IDs and redirect immediately
            console.log('🔍 [VAULT-CONTINUITY] ✅ Existing vault detected - preserving vault IDs and redirecting')
            await refreshServicesAfterVaultChange()
            setVaultResult('✅ Existing vault loaded successfully! Vault IDs preserved.')

            // Navigate to Files page immediately - no template selection needed
            console.log('🔍 [VAULT-CONTINUITY] Redirecting to Files page immediately')
            setTimeout(() => {
              navigate('/files')
            }, 1000) // Shorter delay for existing vaults
          } else {
            // New vault - show template selection
            console.log('🔍 [VAULT-CONTINUITY] New vault location, showing template selection...')
            setShowTemplateSelection(true)
          }
        }
      }
    } catch (error) {
      console.error('Error selecting vault root:', error)
      setVaultResult('❌ Error selecting folder')
    } finally {
      setVaultInitializing(false)
    }
  }

  // Initialize vault with template
  const handleInitializeVault = async (templateType: string) => {
    try {
      setVaultInitializing(true)
      setVaultResult('Initializing vault structure...')

      const initResult = await vaultInitializer.initializeVaultRoot(vaultRootPath, templateType)

      // Handle both APIResponse envelope and direct response for backward compatibility
      const result = initResult.data || initResult

      if (result.success) {
        setVaultResult(`✅ Vault initialized successfully! Created ${result.vaults.length} vault${result.vaults.length !== 1 ? 's' : ''}.`)
        setShowTemplateSelection(false)

        // Refresh vault data
        await loadVaultRoot()

        // Invalidate caches and notify listeners after initialization too
        try {
          await cacheManager.remove('vault_registry')
          await cacheManager.remove('vault_scan_result')
          await cacheManager.remove('vault_cards')
          await window.electronAPI.events?.emit('vault:rootChanged', { vaultRoot: vaultRootPath })
        } catch (e) {
          console.warn('[SETTINGS] Post-initialize cache invalidation/event emit failed:', (e as Error)?.message)
        }

        // Refresh services after vault initialization
        await refreshServicesAfterVaultChange()

        // Navigate to Files page to show the new vault structure
        setTimeout(() => {
          navigate('/files')
        }, 2000) // Give user time to see success message
      } else {
        setVaultResult(`❌ Failed to initialize vault: ${result.error}`)
      }
    } catch (error: any) {
      console.error('Error initializing vault:', error)
      setVaultResult(`❌ Error: ${error.message}`)
    } finally {
      setVaultInitializing(false)
      setTimeout(() => setVaultResult(null), 5000)
    }
  }

  const handleSave = async () => {
    try {
      setIsLoading(true)
      updateSettings(localSettings)

      if (window.electronAPI?.settings) {
        // Save app settings
        await window.electronAPI.settings.set('app-settings', localSettings)
        
        // Save security settings individually for main process access
        if (localSettings.securityLevel) {
          await window.electronAPI.settings.set('securityLevel', localSettings.securityLevel)
        }
        if (localSettings.allowedVaultPatterns !== undefined) {
          await window.electronAPI.settings.set('allowedVaultPatterns', localSettings.allowedVaultPatterns)
        }
      }

      setTestResult('✅ Settings saved successfully!')
      setTimeout(() => setTestResult(null), 3000)
    } catch (error) {
      console.error('Error saving settings:', error)
      setTestResult('❌ Failed to save settings')
      setTimeout(() => setTestResult(null), 3000)
    } finally {
      setIsLoading(false)
    }
  }

  const testApiKey = async () => {
    if (!localSettings.openRouterApiKey) {
      setTestResult('❌ Please enter an API key first')
      return
    }

    try {
      setIsLoading(true)
      setTestResult('Testing API key...')

      // Import the service dynamically to avoid circular dependencies
      const { openRouterService } = await import('../services/openrouter')
      openRouterService.setApiKey(localSettings.openRouterApiKey)

      const validation = await openRouterService.validateApiKey()

      if (validation.valid) {
        setTestResult('✅ API key is valid!')
      } else {
        setTestResult('❌ Invalid API key')
      }
    } catch (error) {
      console.error('Error testing API key:', error)
      setTestResult('❌ Failed to test API key')
    } finally {
      setIsLoading(false)
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
  }



  // Sync active tab to URL
  useEffect(() => {
    const params = new URLSearchParams(window.location.search)
    params.set('tab', activeTab)
    const url = `${window.location.pathname}?${params.toString()}`
    window.history.replaceState({}, '', url)
  }, [activeTab])

  const tabs = [
    { id: 'api', label: 'API Setup', icon: Key },
    { id: 'data', label: 'Data Management', icon: FileText },
    { id: 'security', label: 'Security', icon: Settings },
    { id: 'plugins', label: 'Plugins', icon: Sliders },
    { id: 'profile', label: 'User Profile', icon: User },
    { id: 'test', label: 'Intelligence Test', icon: Settings },
    { id: 'diagnostics', label: 'File Diagnostics', icon: RefreshCw },
    { id: 'fileviewer-test', label: 'File Viewer Test', icon: FileText },
  ]

  return (
    <div className="h-screen flex flex-col bg-gray-900 text-supplement1">
      {/* Header */}
      <header className="flex items-center gap-4 h-16 px-6 border-b border-tertiary bg-gray-800/60 backdrop-blur-lg">
        <button
          onClick={() => navigate('/')}
          className="u1-button-ghost h-8 w-8 flex items-center justify-center"
        >
          <ArrowLeft className="h-4 w-4" />
        </button>
        <div className="flex items-center gap-2">
          <Settings className="h-5 w-5 text-primary" />
          <h1 className="text-xl font-semibold text-supplement1">Settings</h1>
        </div>
      </header>

      <div className="flex-1 flex">
        {/* Sidebar */}
        <nav className="w-64 border-r border-tertiary bg-gray-800/30">
          <div className="p-4">
            <div className="space-y-1">
              {tabs.map((tab) => {
                const Icon = tab.icon
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`
                      w-full flex items-center gap-3 px-3 py-2 rounded-lg text-sm transition-colors
                      ${activeTab === tab.id
                        ? 'bg-primary text-gray-900'
                        : 'text-gray-400 hover:text-supplement1 hover:bg-gray-700'
                      }
                    `}
                  >
                    <Icon className="h-4 w-4" />
                    {tab.label}
                  </button>
                )
              })}
            </div>
          </div>
        </nav>

        {/* Main Content */}
        <main className="flex-1 overflow-y-auto">
          <div className="max-w-4xl mx-auto p-8">
            {activeTab === 'api' && (
              <div className="space-y-6">
                <div>
                  <h2 className="text-2xl font-semibold mb-2 text-supplement1">API Configuration</h2>
                  <p className="text-gray-400">Configure your OpenRouter API key to access AI models.</p>
                </div>

                <div className="u1-card bg-gray-800/50 border border-gray-700">
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium mb-2 text-supplement1">
                        OpenRouter API Key
                      </label>
                      <div className="flex gap-3">
                        <input
                          type="password"
                          value={localSettings.openRouterApiKey || ''}
                          onChange={(e) => setLocalSettings(prev => ({
                            ...prev,
                            openRouterApiKey: e.target.value
                          }))}
                          placeholder="sk-or-..."
                          className="u1-input-field flex-1"
                        />
                        <button
                          onClick={testApiKey}
                          disabled={isLoading}
                          className="u1-button-ghost px-4 py-2 disabled:opacity-50"
                        >
                          Test
                        </button>
                      </div>
                      {testResult && (
                        <p className="mt-2 text-sm">{testResult}</p>
                      )}
                    </div>

                    <div className="flex gap-3">
                      <button
                        onClick={handleSave}
                        disabled={isLoading}
                        className="u1-button-primary disabled:opacity-50"
                      >
                        Save Settings
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'data' && (
              <div className="space-y-6">
                <div>
                  <h2 className="text-2xl font-semibold mb-2 text-supplement1">Data Management</h2>
                  <p className="text-gray-400">Manage your files, conversations, and storage.</p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Context Vault Root */}
                  <div className="u1-card bg-gray-800/50 border border-gray-700">
                    <h3 className="text-lg font-medium mb-4">Context Vault Root</h3>
                    <p className="text-gray-400 text-sm mb-4">
                      Choose where to store your context vaults and organized files.
                    </p>

                    <div className="space-y-3">
                      <div>
                        <label className="block text-sm font-medium mb-2">
                          Vault Root Location
                        </label>
                        <div className="flex gap-2">
                          <input
                            type="text"
                            value={vaultRootPath}
                            readOnly
                            placeholder="No vault root selected"
                            className="flex-1 bg-neutral-800 border border-neutral-700 rounded-lg px-3 py-2 text-sm font-mono text-neutral-300"
                          />
                          <button
                            onClick={handleSelectVaultRoot}
                            disabled={vaultInitializing}
                            className="px-3 py-2 bg-neutral-700 hover:bg-neutral-600 text-white rounded-lg text-sm font-medium transition-colors flex items-center gap-2 disabled:opacity-50"
                          >
                            <Folder size={16} />
                            {vaultInitializing ? 'Loading...' : 'Browse'}
                          </button>
                        </div>
                      </div>

                      {/* Portable Mode Toggle */}
                      <div className="mt-4">
                        <label className="flex items-center gap-3 cursor-pointer">
                          <input
                            type="checkbox"
                            checked={portableModeEnabled}
                            onChange={(e) => handlePortableModeToggle(e.target.checked)}
                            disabled={portableModeLoading || !vaultRootPath}
                            className="w-4 h-4 text-blue-600 bg-neutral-800 border-neutral-700 rounded focus:ring-blue-500 focus:ring-2"
                          />
                          <span className="text-sm font-medium">
                            Store database on USB for portability
                          </span>
                        </label>
                        <p className="text-xs text-neutral-500 mt-1 ml-7">
                          When enabled, your database will be stored on the USB drive alongside your vault files
                        </p>
                      </div>

                      {portableModeResult && (
                        <div className={`text-sm p-3 rounded-lg mt-3 ${
                          portableModeResult.startsWith('✅') 
                            ? 'bg-green-700/20 border border-green-600/30 text-green-300' 
                            : 'bg-red-700/20 border border-red-600/30 text-red-300'
                        }`}>
                          {portableModeResult}
                        </div>
                      )}

                      {vaultResult && (
                        <div className="text-sm p-3 bg-gray-700 rounded-lg">
                          {vaultResult}
                        </div>
                      )}

                      {/* Template Selection Modal */}
                      {showTemplateSelection && (
                        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
                          <div className="bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4 border border-gray-700">
                            <h3 className="text-lg font-medium mb-4">Choose Vault Template</h3>
                            <p className="text-gray-400 text-sm mb-6">
                              Select how you'd like to organize your context vaults:
                            </p>

                            <div className="space-y-3">
                              <button
                                onClick={() => handleInitializeVault('default')}
                                disabled={vaultInitializing}
                                className="w-full p-4 bg-gray-700 hover:bg-gray-600 rounded-lg text-left transition-colors disabled:opacity-50"
                              >
                                <div className="font-medium">Default Setup</div>
                                <div className="text-sm text-gray-400">Personal & Work vaults with getting started context</div>
                              </button>

                              <button
                                onClick={() => handleInitializeVault('simple')}
                                disabled={vaultInitializing}
                                className="w-full p-4 bg-gray-700 hover:bg-gray-600 rounded-lg text-left transition-colors disabled:opacity-50"
                              >
                                <div className="font-medium">Simple Setup</div>
                                <div className="text-sm text-gray-400">Single vault to get started</div>
                              </button>
                            </div>

                            <div className="flex gap-2 mt-6">
                              <button
                                onClick={() => setShowTemplateSelection(false)}
                                disabled={vaultInitializing}
                                className="flex-1 px-4 py-2 bg-gray-600 hover:bg-gray-500 text-white rounded-lg transition-colors disabled:opacity-50"
                              >
                                Cancel
                              </button>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* File Storage Stats */}
                  {storageInfo && (
                    <div className="u1-card bg-gray-800/50 border border-gray-700">
                      <h3 className="text-lg font-medium mb-4">File Storage</h3>
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div className="flex justify-between">
                          <span className="text-neutral-400">Total Files:</span>
                          <span className="text-neutral-200">{storageInfo.totalFiles}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-neutral-400">Total Size:</span>
                          <span className="text-neutral-200">{formatFileSize(storageInfo.totalSize)}</span>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* File Types */}
                  {storageInfo && (
                    <div className="bg-neutral-900/50 border border-neutral-800 rounded-lg p-6">
                      <h3 className="text-lg font-medium mb-4">File Types</h3>
                      <div className="space-y-2">
                        {Object.entries(storageInfo.fileTypes).map(([type, count]) => (
                          <div key={type} className="flex justify-between text-sm">
                            <span className="text-neutral-400 capitalize">{type}:</span>
                            <span className="text-neutral-200">{String(count)}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {activeTab === 'security' && (
              <div className="space-y-6">
                <div>
                  <h2 className="text-2xl font-semibold mb-2 text-supplement1">Security Framework V2</h2>
                  <p className="text-gray-400">Configure data privacy protection for external LLM communications and sensitive content detection.</p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Security Level */}
                  <div className="u1-card bg-gray-800/50 border border-gray-700">
                    <h3 className="text-lg font-medium mb-4">🛡️ Security Level</h3>
                    <p className="text-gray-400 text-sm mb-4">
                      Control how ChatLo handles sensitive content when communicating with external LLMs.
                    </p>

                    <div className="space-y-3">
                      <div>
                        <label className="block text-sm font-medium mb-2">
                          Data Privacy Mode
                        </label>
                        <select
                          value={localSettings.securityLevel || 'balanced'}
                          onChange={(e) => setLocalSettings(prev => ({
                            ...prev,
                            securityLevel: e.target.value
                          }))}
                          className="w-full bg-neutral-800 border border-neutral-700 rounded-lg px-3 py-2 text-sm text-neutral-300"
                        >
                          <option value="strict">🔴 Strict - Block sensitive content</option>
                          <option value="balanced">🟡 Balanced - Ask permission (Default)</option>
                          <option value="disabled">🟢 Disabled - Allow all content</option>
                        </select>
                      </div>

                      <div className="text-xs text-gray-500 space-y-1">
                        <p><strong>Strict:</strong> Blocks emails, patents, contracts, PII from external LLMs</p>
                        <p><strong>Balanced:</strong> Asks permission before sharing sensitive content</p>
                        <p><strong>Disabled:</strong> Shows reminder only, allows all content sharing</p>
                      </div>
                    </div>
                  </div>

                  {/* Custom Exception Patterns */}
                  <div className="u1-card bg-gray-800/50 border border-gray-700">
                    <h3 className="text-lg font-medium mb-4">🔓 Custom Exception Patterns</h3>
                    <p className="text-gray-400 text-sm mb-4">
                      Define regex patterns to allow specific sensitive content. These patterns override security filtering.
                    </p>

                    <div className="space-y-3">
                      <div>
                        <label className="block text-sm font-medium mb-2">
                          Exception Patterns (Regex)
                        </label>
                        <textarea
                          value={localSettings.allowedVaultPatterns || ''}
                          onChange={(e) => setLocalSettings(prev => ({
                            ...prev,
                            allowedVaultPatterns: e.target.value
                          }))}
                          placeholder="Enter regex patterns, one per line&#10;Examples:&#10;patent.*(?:graph|diagram)&#10;proprietary.*(?:algorithm|research)&#10;confidential.*(?:strategy|planning)"
                          rows={4}
                          className="w-full bg-neutral-800 border border-neutral-700 rounded-lg px-3 py-2 text-sm text-neutral-300 font-mono"
                        />
                      </div>

                      <div className="text-xs text-gray-500">
                        <p>• <strong>patent.*analysis</strong> - Allow patent-related analysis discussions</p>
                        <p>• <strong>proprietary.*research</strong> - Allow proprietary research sharing</p>
                        <p>• <strong>confidential.*strategy</strong> - Allow business strategy discussions</p>
                        <p>• One pattern per line, uses JavaScript regex syntax</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Security Status */}
                <div className="u1-card bg-gray-800/50 border border-gray-700">
                  <h3 className="text-lg font-medium mb-4">📊 Security Status</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div className="flex items-center gap-2">
                      <div className={`w-3 h-3 rounded-full ${
                        (localSettings.securityLevel || 'balanced') === 'strict' ? 'bg-red-500' :
                        (localSettings.securityLevel || 'balanced') === 'balanced' ? 'bg-yellow-500' : 'bg-green-500'
                      }`}></div>
                      <span className="text-neutral-400">Security Level:</span>
                      <span className="text-neutral-200 capitalize">{localSettings.securityLevel || 'balanced'}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 rounded-full bg-blue-500"></div>
                      <span className="text-neutral-400">Pattern Detection:</span>
                      <span className="text-neutral-200">Active</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 rounded-full bg-green-500"></div>
                      <span className="text-neutral-400">Local Models:</span>
                      <span className="text-neutral-200">Always Allowed</span>
                    </div>
                  </div>

                  <div className="mt-4 p-3 bg-gray-900/50 rounded-lg">
                    <p className="text-xs text-gray-400">
                      <strong>Framework V2:</strong> Smart pattern detection with context-aware filtering.
                      Protects PII, contracts, patents, and proprietary information from external LLMs while allowing legitimate use cases.
                    </p>
                  </div>
                </div>

                {/* Apply Button */}
                <div className="flex gap-3">
                  <button
                    onClick={handleSave}
                    disabled={isLoading}
                    className="u1-button-primary disabled:opacity-50"
                  >
                    Apply Security Settings
                  </button>
                  <button
                    onClick={() => {
                      setLocalSettings(prev => ({
                        ...prev,
                        securityLevel: 'balanced',
                        allowedVaultPatterns: ''
                      }))
                    }}
                    className="u1-button-ghost px-4 py-2 disabled:opacity-50"
                  >
                    Reset to Default
                  </button>
                </div>
              </div>
            )}

            {activeTab === 'profile' && (
              <div className="space-y-6">
                <div>
                  <h2 className="text-2xl font-semibold mb-2">User Profile</h2>
                  <p className="text-neutral-400">Manage your profile and preferences.</p>
                </div>

                <div className="bg-neutral-900/50 border border-neutral-800 rounded-lg p-6">
                  <div className="text-center py-8">
                    <User className="h-12 w-12 text-neutral-500 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-neutral-400 mb-2">Coming Soon</h3>
                    <p className="text-sm text-neutral-500">
                      User profile features will be available in a future update.
                    </p>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'plugins' && (
              <div className="space-y-6">
                <div>
                  <h2 className="text-2xl font-semibold mb-2 text-supplement1">Plugin Management</h2>
                  <p className="text-gray-400">Manage your plugins and extensions.</p>
                </div>

                <PluginManager />
              </div>
            )}

            {activeTab === 'test' && (
              <div className="space-y-6">
                <div>
                  <h2 className="text-2xl font-semibold mb-2 text-supplement1">Intelligence System Test</h2>
                  <p className="text-gray-400">Test the intelligence extraction and performance monitoring systems.</p>
                </div>

                <IntelligenceTestLauncher />

                <div className="border-t border-tertiary/30 pt-6">
                  <h3 className="text-xl font-semibold mb-4 text-supplement1">Analytics Dashboard</h3>
                  <IntelligenceAnalyticsDashboard />
                </div>
              </div>
            )}

            {activeTab === 'diagnostics' && (
              <div className="space-y-6">
                <div>
                  <h2 className="text-2xl font-semibold mb-2 text-supplement1">File Processing Diagnostics</h2>
                  <p className="text-gray-400">Diagnose file processing issues and troubleshoot PDF parsing problems.</p>
                </div>

                <div className="bg-neutral-800/50 rounded-lg p-6 border border-neutral-600">
                  <h3 className="text-lg font-semibold mb-4 text-white">Diagnostic Tool</h3>
                  <p className="text-neutral-300 mb-4">
                    Use this tool to diagnose file processing issues. It will check file system access,
                    plugin availability, content processing, and database integration.
                  </p>
                  <button
                    onClick={() => setShowDiagnostics(true)}
                    className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors flex items-center gap-2"
                  >
                    <RefreshCw className="w-4 h-4" />
                    Open Diagnostics Tool
                  </button>
                </div>
              </div>
            )}

            {activeTab === 'fileviewer-test' && (
              <div className="space-y-6">
                <div>
                  <h2 className="text-2xl font-semibold mb-2 text-supplement1">File Viewer Test Suite</h2>
                  <p className="text-gray-400">Test Office document binary presentation and parsing capabilities under the same tech stack.</p>
                </div>

                <div className="bg-neutral-800/50 rounded-lg p-6 border border-neutral-600">
                  <h3 className="text-lg font-semibold mb-4 text-white">Office Document Viewer Test</h3>
                  <p className="text-neutral-300 mb-4">
                    This test suite validates binary file handling, document parsing, and viewer presentation
                    using the same React + TypeScript stack as the main DocumentViewer component.
                  </p>
                  <div className="space-y-3">
                    <div className="text-sm text-neutral-400">
                      <p>• Tests binary data processing and Office document detection</p>
                      <p>• Validates error handling for corrupted and unsupported files</p>
                      <p>• Simulates real document loading scenarios</p>
                      <p>• Provides detailed logging for debugging purposes</p>
                    </div>
                    <button
                      onClick={() => setShowFileViewerTest(true)}
                      className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors flex items-center gap-2"
                    >
                      <FileText className="w-4 h-4" />
                      Launch File Viewer Test
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </main>
      </div>

      {/* File Processing Diagnostics Modal */}
      <FileProcessingDiagnostics
        isOpen={showDiagnostics}
        onClose={() => setShowDiagnostics(false)}
      />

      {/* File Viewer Test Modal */}
      <FileViewerTestPage
        isOpen={showFileViewerTest}
        onClose={() => setShowFileViewerTest(false)}
      />
    </div>
  )
}

export default SettingsPage
