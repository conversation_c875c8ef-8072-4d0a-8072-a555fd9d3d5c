import React, { useEffect, useRef, useState } from 'react'
import { useSearchParams } from 'react-router-dom'
import { useAppStore } from '../store'
import { vaultContextService } from '../services/vaultContextService'
import MessageBubble from './MessageBubble'
import StreamingMessageBubble from './StreamingMessageBubble'
import InputArea from './InputArea'
import { Bot } from './Icons'
import Icon from './Icons/Icon'
import { db, events, files } from '../api/UnifiedAPIClient'

import { ContextVaultSelector } from './ContextVaultSelector'

// Debug component to show context and storage information
const DebugInfoRow: React.FC<{ 
  selectedContextId: string | null
  vaultRegistry: any
  chatNotesLocation: string
}> = ({ selectedContextId, vaultRegistry, chatNotesLocation }) => {
  if (!selectedContextId && !vaultRegistry) return null

  return (
    <div className="bg-green-900/20 border-b border-green-500/30 px-4 md:px-6 py-1">
      <div className="max-w-4xl mx-auto">
        <div className="flex items-center gap-4 text-xs text-green-400 font-mono">
          <span>🔍 DEBUG:</span>
          <span>Context: {selectedContextId || 'None'}</span>
          <span>|</span>
          <span>Chat-notes: {chatNotesLocation}</span>
          <span>|</span>
          <span>Vaults: {vaultRegistry?.vaults?.length || 0}</span>
          {vaultRegistry?.vaultRoot && (
            <>
              <span>|</span>
              <span>Root: {vaultRegistry.vaultRoot}</span>
            </>
          )}
        </div>
      </div>
    </div>
  )
}

const ChatArea: React.FC = () => {
  const [searchParams] = useSearchParams()
  const {
    currentConversationId,
    draftConversationId,
    saveDraftConversation,
    messages,
    conversations,
    isLoading,
    streamingMessage,
    togglePinMessage,
    setCurrentConversation,
    loadMessages
  } = useAppStore()

  // Context vault state
  const [selectedContextId, setSelectedContextId] = useState<string | null>(null)
  const [vaultRegistry, setVaultRegistry] = useState<any>(null)
  const [chatNotesLocation, setChatNotesLocation] = useState<string>('')

  // Check for context parameter in URL
  useEffect(() => {
    const contextFromUrl = searchParams.get('context')
    console.log('[DEBUG] 🎯 CRITICAL: ChatArea URL params check:', {
      contextFromUrl,
      currentSelectedContextId: selectedContextId,
      allParams: Object.fromEntries(searchParams.entries()),
      searchParams: searchParams.toString(),
      location: window.location.href
    })
    if (contextFromUrl) {
      console.log('[DEBUG] 🎯 CRITICAL: Setting context from URL:', contextFromUrl)
      setSelectedContextId(contextFromUrl)
    } else {
      console.log('[DEBUG] 🎯 CRITICAL: No context in URL, keeping current:', selectedContextId)
    }
  }, [searchParams])

  // Fetch vault registry for debug info
  useEffect(() => {
    const fetchVaultInfo = async () => {
      try {
        if (window.electronAPI?.vault?.getVaultRegistry) {
          const reg = await window.electronAPI.vault.getVaultRegistry()
          const data = (reg as any)?.data || reg
          setVaultRegistry(data)
        }
      } catch (error) {
        console.warn('[DEBUG] Failed to fetch vault registry:', error)
      }
    }

    fetchVaultInfo()
  }, [])

  // Calculate chat-notes location based on CURRENTLY SELECTED context
  useEffect(() => {
    if (!vaultRegistry) return

    let location: string

    if (selectedContextId) {
      // User has selected a specific context - find it in the registry
      const selectedVault = vaultRegistry.vaults?.find((vault: any) =>
        vault.contexts?.some((context: any) => context.id === selectedContextId)
      )

      if (selectedVault) {
        const selectedContext = selectedVault.contexts?.find((context: any) => context.id === selectedContextId)
        if (selectedContext) {
          location = `${selectedContext.path}/.intelligence/context-notes`
          console.log('[DEBUG] Chat-notes will be stored in selected context:', location)
        } else {
          location = `${selectedVault.path}/.intelligence/context-notes`
          console.log('[DEBUG] Chat-notes will be stored in selected vault:', location)
        }
      } else {
        // Fallback to shared-dropbox if selected context not found
        location = `${vaultRegistry.vaultRoot}/shared-dropbox/.intelligence/context-notes`
        console.log('[DEBUG] Selected context not found, falling back to shared-dropbox:', location)
      }
    } else {
      // No context selected - use shared-dropbox as default
      location = `${vaultRegistry.vaultRoot}/shared-dropbox/.intelligence/context-notes`
      console.log('[DEBUG] No context selected, using shared-dropbox:', location)
    }

    setChatNotesLocation(location)
  }, [selectedContextId, vaultRegistry])

  const handleContextSelect = (contextId: string | null) => {
    setSelectedContextId(contextId)
    // Note: Context selection is local here; planned centralization in store with URL param sync per architecture docs
    console.log('Selected context:', contextId)
  }

  const messagesEndRef = useRef<HTMLDivElement>(null)
  const [isVirtualized, setIsVirtualized] = useState(false)
  const messagesContainerRef = useRef<HTMLDivElement>(null)
  const [renderRange, setRenderRange] = useState({ start: 0, end: 50 })
  const itemHeight = 80 // Default height estimate for virtual scrolling
  const [containerHeight, setContainerHeight] = useState(0)

  const currentConversation = conversations.find(c => c.id === currentConversationId)

  // Handle conversation parameter from URL (e.g., from history page)
  // Important: do not require the conversation to already be present in store
  // We trust the URL param and initialize the view directly, then the store will catch up.
  useEffect(() => {
    const conversationParam = searchParams.get('conversation')
    if (conversationParam && conversationParam !== currentConversationId) {
      setCurrentConversation(conversationParam)
      loadMessages(conversationParam)
    }
  }, [searchParams, currentConversationId, setCurrentConversation, loadMessages])

  // Deep-link: if URL includes file and (optional) entities/selectedText, index file and attach to a greeting message
  const deepLinkHandledRef = useRef(false)
  useEffect(() => {
    const handleDeepLink = async () => {
      if (deepLinkHandledRef.current) return
      const filePath = searchParams.get('file')
      const fileName = searchParams.get('filename') || searchParams.get('name') || undefined
      const entitiesParam = searchParams.get('entities')
      const selectedText = searchParams.get('selectedText') || undefined
      const actionType = searchParams.get('action') || undefined
      const vaultName = searchParams.get('vault') || undefined
      const contextParam = searchParams.get('context') || undefined
      const conversationParam = searchParams.get('conversation') || undefined

      // Require an active conversation and a file to proceed
      let convIdRaw: any = conversationParam || currentConversationId

      console.log('[CHAT-AREA] 🔍 Raw conversation parameter analysis:', {
        conversationParam,
        conversationParamType: typeof conversationParam,
        currentConversationId,
        currentConversationIdType: typeof currentConversationId,
        convIdRaw,
        convIdRawType: typeof convIdRaw
      })

      // Extract string ID from various response formats
      let convId: string = ''

      // Handle the '[object Object]' string case (URL-encoded object)
      if (typeof convIdRaw === 'string' && convIdRaw === '[object Object]') {
        console.error('[CHAT-AREA] ❌ Received literal "[object Object]" string - conversation creation failed')
        convId = ''
      } else if (typeof convIdRaw === 'string' && convIdRaw !== '[object Object]') {
        convId = convIdRaw
      } else if (convIdRaw?.data && typeof convIdRaw.data === 'string') {
        convId = convIdRaw.data
      } else if (convIdRaw?.id && typeof convIdRaw.id === 'string') {
        convId = String(convIdRaw.id)
      } else if (convIdRaw && typeof convIdRaw === 'object') {
        // Try to extract ID from object
        const extractedId = convIdRaw.id || convIdRaw.data || convIdRaw.conversationId
        convId = extractedId ? String(extractedId) : ''
      }

      console.log('[CHAT-AREA] 🔧 Deep link conversation analysis:', {
        conversationParam,
        currentConversationId,
        convIdRaw,
        convId,
        convIdType: typeof convId,
        draftConversationId,
        vaultName,
        contextParam
      })

      // UNIFIED FIX: Set vault context using unified service
      if (contextParam || vaultName) {
        try {
          console.log('[CHAT-AREA] 🎯 Setting vault context from URL parameters:', { contextParam, vaultName })

          // Get vault registry to resolve context
          const registry = await window.electronAPI?.vault?.getVaultRegistry()
          if (registry?.vaults) {
            // Find the vault and context
            const vault = registry.vaults.find((v: any) =>
              v.path.includes(vaultName) || v.contexts?.some((c: any) => c.id === contextParam)
            )

            if (vault) {
              const context = vault.contexts?.find((c: any) => c.id === contextParam) || vault.contexts?.[0]

              if (context) {
                await vaultContextService.setCurrentContext({
                  vaultPath: vault.path,
                  contextId: context.id,
                  contextName: context.name,
                  contextPath: context.path
                })

                console.log('[CHAT-AREA] ✅ Vault context set successfully:', {
                  vault: vault.path,
                  context: context.id
                })
              }
            }
          }
        } catch (e) {
          console.warn('[CHAT-AREA] ⚠️ Failed to set vault context from URL parameter:', e)
        }
      }

      // Only require convId for deep link handling - filePath is optional for AI Actions
      if (!convId) {
        console.warn('[CHAT-AREA] ❌ Missing convId:', { convId, filePath })
        return
      }

      // If no filePath, this is just a conversation navigation (not an AI Action)
      if (!filePath) {
        console.log('[CHAT-AREA] 🔗 Deep link conversation navigation (no file):', { convId })
        // Just ensure the conversation is loaded
        await loadMessages(convId)
        return
      }

      // If we are in a draft conversation, save it first to get a real ID (avoid FK failures)
      try {
        if (draftConversationId && convId === draftConversationId) {
          console.log('[CHAT-AREA] 🔧 Draft conversation detected; saving before AI Action message')
          const realId = await saveDraftConversation(draftConversationId)
          convId = String(realId)
          setCurrentConversation(convId)
          console.log('[CHAT-AREA] 🔧 Draft saved, new convId:', convId)
          // Keep URL param in sync with the real conversation id
          try {
            const hash = window.location.hash || '#/chat'
            const [route, query = ''] = hash.split('?')
            const params = new URLSearchParams(query)
            params.set('conversation', convId)
            const newHash = `${route}?${params.toString()}`
            if (newHash !== hash) {
              window.location.hash = newHash
            }
          } catch {}
        }
      } catch (e) {
        console.warn('[CHAT-AREA] ⚠️ Failed to save draft conversation before AI Action message:', e)
      }

      // Final safety check - ensure convId is a string
      if (typeof convId !== 'string' || convId === '[object Object]') {
        console.error('[CHAT-AREA] ❌ Invalid conversation ID after processing:', { convId, type: typeof convId })
        return
      }

      try {
        deepLinkHandledRef.current = true
        // Dedupe repeated insertions across remounts for this (conversation,file)
        const dedupeKey = `deeplink:${convId}:${filePath}`
        if (sessionStorage.getItem(dedupeKey)) return
        sessionStorage.setItem(dedupeKey, '1')

        // Keep URL param in sync (HashRouter): update the hash fragment query only
        try {
          const hash = window.location.hash || '#/chat'
          const [route, query = ''] = hash.split('?')
          const params = new URLSearchParams(query)
          params.set('conversation', String(convId))
          const newHash = `${route}?${params.toString()}`
          if (newHash !== hash) {
            window.location.hash = newHash
          }
        } catch {}

        // Index the file to get a file ID
        let fileId: string | undefined
        if (filePath) {
          try {
            const indexResult = await files.indexFile(filePath, true)
            console.log('[CHAT-AREA] 🔗 File indexing result:', indexResult)
            
            // CRITICAL FIX: The indexFile API returns a FileDescriptor, not a database record
            // We need to ensure the file gets added to the database to get an actual ID
            if (indexResult && typeof indexResult === 'object') {
              console.log('[CHAT-AREA] 🔗 Got file descriptor:', indexResult)
              
              // CRITICAL FIX: Extract vault and context information from file path if not provided by indexFile
              const descriptor: any = (indexResult as any)?.data || (indexResult as any)
              let vaultName = descriptor.vaultPath || ''
              let relativePath = descriptor.relativePath || ''

              if (!vaultName || !relativePath) {
                // Parse file path: {vaultRoot}\\{vaultName}\\{contextName}\\{subfolder}\\{filename}
                const pathParts = filePath.split(/[\\/]/)
                const vaultRoot = vaultRegistry?.vaultRoot || (filePath.includes('Post-Kernel-Test4') ? 'Post-Kernel-Test4' : null)
                const vaultRootIndex = vaultRoot ? pathParts.findIndex(part => part === vaultRoot) : -1
                if (vaultRootIndex >= 0 && vaultRootIndex + 1 < pathParts.length) {
                  vaultName = pathParts[vaultRootIndex + 1]
                  if (vaultRootIndex + 2 < pathParts.length) {
                    const contextName = pathParts[vaultRootIndex + 2]
                    const remainingPath = pathParts.slice(vaultRootIndex + 3).join('/')
                    relativePath = `${contextName}/${remainingPath}`
                  }
                }
                console.log('[CHAT-AREA] 🔧 Extracted vault info from path:', { vaultName, relativePath })
              }

              // Try to add the file to the database to get a proper ID
              try {
                const fileRecord = await db.files.add({
                  filename: fileName || filePath.split(/[\\/]/).pop() || 'unknown',
                  filepath: descriptor.filePath || filePath,
                  file_type: descriptor.mime || 'application/octet-stream',
                  file_size: descriptor.size || 0,
                  content_hash: descriptor.contentHash || '',
                  mime_type: descriptor.mime || 'application/octet-stream',
                  vault_name: vaultName,
                  relative_path: relativePath,
                  storage_type: 'vault',
                  metadata: JSON.stringify({
                    lastModified: descriptor.modified || new Date().toISOString(),
                    isDirectory: false,
                    extension: (descriptor.filePath || filePath).split('.').pop() || '',
                    source: 'chatlo-deeplink'
                  })
                })

                if (fileRecord && typeof fileRecord === 'string') {
                  fileId = fileRecord
                  console.log('[CHAT-AREA] ✅ Created database record with ID:', fileId)

                  // Ensure parsed text is ready using the same kernel pipeline as DocumentViewer
                  try {
                    if (window.electronAPI?.files?.processFile) {
                      const processed = await window.electronAPI.files.processFile(filePath)
                      const text = processed?.content?.text || ''
                      const meta = processed?.content?.metadata || {}
                      if (text && text.length > 0) {
                        // Persist into DB for downstream chat code that expects extracted_content
                        try {
                          await db.files.update(fileId, {
                            extracted_content: text,
                            metadata: JSON.stringify({
                              ...(typeof meta === 'object' ? meta : {}),
                              processed: true,
                              source: 'kernel-artifacts',
                              updatedAt: new Date().toISOString()
                            })
                          })
                          console.log('[CHAT-AREA] ✅ Persisted parsed text to DB (chars=', text.length, ')')
                        } catch (e) {
                          console.warn('[CHAT-AREA] ⚠️ Failed to persist parsed text to DB:', e)
                        }
                      }
                    }
                  } catch (e) {
                    console.warn('[CHAT-AREA] ⚠️ Kernel processing failed in deep-link step (will fallback later):', e)
                  }
                } else {
                  console.warn('[CHAT-AREA] ❌ Failed to create database record:', fileRecord)
                }
              } catch (dbError) {
                console.warn('[CHAT-AREA] ❌ Database add failed:', dbError)
                console.warn('[CHAT-AREA] ❌ Attempted to add file record:', {
                  filename: fileName || filePath.split(/[/\\]/).pop() || 'unknown',
                  filepath: descriptor.filePath || filePath,
                  file_type: descriptor.mime || 'application/octet-stream',
                  file_size: descriptor.size || 0,
                  content_hash: descriptor.contentHash || '',
                  mime_type: descriptor.mime || 'application/octet-stream',
                  vault_name: descriptor.vaultPath || '',
                  relative_path: descriptor.relativePath || '',
                  storage_type: 'vault'
                })
              }
            } else {
              console.warn('[CHAT-AREA] ❌ Invalid indexing result:', indexResult)
            }
          } catch (e) {
            console.warn('Failed to index file:', e)
          }
        }

        // Build initial assistant message content based on action type
        let intro = 'File is parsed and ready.'
        if (fileName) {
          // Include the full file path so MessageBubble can render it as a chat link
          intro = `File ${filePath} is parsed and ready. `
          
          switch (actionType) {
            case 'summarize':
              intro += `I can help you create a comprehensive summary of this document.`
              break
            case 'extract':
              intro += `I can help you identify and extract the most important information from this document.`
              break
            case 'chat':
              intro += `I can help you explore and discuss any aspect of this document.`
              break
            case 'questions':
              intro += `I can help you create insightful questions based on this document's content.`
              break
            default:
              intro += `I can help you analyze this document.`
          }
        }

        // Add vault context if available
        if (vaultName && vaultName !== 'Shared Dropbox') {
          intro += `\n\nThis file is from the "${vaultName}" vault.`
        }

        // Append entities/selected text context if provided
        try {
          if (entitiesParam) {
            const entities = JSON.parse(entitiesParam)
            if (Array.isArray(entities) && entities.length > 0) {
              intro += `\n\nFocus areas: ${entities.slice(0, 8).join(', ')}`
            }
          }
        } catch {}
        
        if (selectedText && selectedText.trim().length > 0) {
          intro += `\n\nSelected text: "${selectedText.substring(0, 200)}${selectedText.length > 200 ? '…' : ''}"`
        }

        // Add action-specific guidance
        if (actionType) {
          switch (actionType) {
            case 'summarize':
              intro += `\n\nTo get started, you can ask me to:\n• Provide a comprehensive summary\n• Create an executive summary\n• Summarize specific sections\n• Highlight key themes and insights`
              break
            case 'extract':
              intro += `\n\nTo get started, you can ask me to:\n• Extract main arguments and supporting evidence\n• Identify key data points and statistics\n• List important dates and events\n• Find actionable insights and recommendations`
              break
            case 'chat':
              intro += `\n\nTo get started, you can ask me to:\n• Explain any concept or section in detail\n• Compare different parts of the document\n• Discuss implications and applications\n• Answer specific questions about the content`
              break
            case 'questions':
              intro += `\n\nTo get started, you can ask me to:\n• Generate discussion questions\n• Create quiz questions for learning\n• Formulate research questions\n• Develop interview questions based on the content`
              break
          }
        }

        // Create assistant message and attach file
        const safeConvId = String(convId)
        console.log('[CHAT-AREA] 🔧 Creating assistant intro message with convId:', safeConvId)
        const messageId = await db.messages.add(safeConvId, {
          conversation_id: safeConvId,
          role: 'assistant',
          content: intro,
          model: 'system'
        })
        console.log('[CHAT-AREA] ✅ Created assistant intro message with ID:', messageId)

        if (fileId) {
          try {
            console.log('[CHAT-AREA] 🔗 Attempting to attach file:', { messageId, fileId, type: 'attachment' })
            const attachResult = await files.addFileAttachment(messageId, fileId, 'attachment')
            console.log('[CHAT-AREA] ✅ File attachment result:', attachResult)

            // Also add file attachment to conversation for proper context building
            console.log('[CHAT-AREA] 🔗 Adding file attachment to conversation:', { convId: safeConvId, fileId })
            const convAttachmentId = await db.files.addFileAttachment(safeConvId, fileId)
            console.log('[CHAT-AREA] ✅ Conversation file attachment ID:', convAttachmentId)
          } catch (e) {
            console.warn('[CHAT-AREA] ❌ Failed to attach file:', e)
          }
        } else {
          console.warn('[CHAT-AREA] ❌ No file ID available for attachment')
        }

        await loadMessages(convId)
      } catch (e) {
        console.warn('Deep link chat setup failed:', e)
      }
    }

    handleDeepLink()
  }, [searchParams, currentConversationId, loadMessages])

  // Minimal events subscription (no UI change)
  useEffect(() => {
    let subscriptionId: string | undefined
    ;(async () => {
      try {
        const sub = await events.subscribe('task')
        if (typeof sub === 'string') {
          subscriptionId = sub
        } else if (sub && typeof sub === 'object') {
          // Support both plain result and API-wrapped shapes
          subscriptionId = (sub as any).subscriptionId || (sub as any).data?.subscriptionId
        }
      } catch (e) {
        // no-op
      }
    })()
    return () => {
      if (subscriptionId) {
        events.unsubscribe(subscriptionId).catch(() => {})
      }
    }
  }, [])

  const scrollToBottom = () => {
    // Use requestAnimationFrame for better timing with DOM updates
    requestAnimationFrame(() => {
      setTimeout(() => {
        if (messagesEndRef.current) {
          messagesEndRef.current.scrollIntoView({
            behavior: 'smooth',
            block: 'end',
            inline: 'nearest'
          })
        }
      }, 50)
    })
  }

  const handleRegenerate = async (messageId: string) => {
    console.log('Regenerating message:', messageId)
    const { sendMessage, messages, currentConversationId } = useAppStore.getState()
    const messageToRegenerate = messages.find(m => m.id === messageId)
    console.log('Message to regenerate:', messageToRegenerate)
    
    if (messageToRegenerate && currentConversationId) {
      // Find the user message that preceded the assistant's message
      const precedingUserMessage = messages
        .slice(0, messages.findIndex(m => m.id === messageId))
        .reverse()
        .find(m => m.role === 'user')
      console.log('Preceding user message:', precedingUserMessage)

      if (precedingUserMessage) {
        await sendMessage(precedingUserMessage.content)
      } else {
        console.error('Could not find preceding user message to regenerate from.')
      }
    } else {
      console.error('Could not find message to regenerate or no current conversation.')
    }
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages, streamingMessage])

  // Note: streaming scroll handling is covered by the effect on [messages, streamingMessage]

  // Virtual scrolling implementation - observe container size changes
  useEffect(() => {
    const container = messagesContainerRef.current
    if (!container) return

    const resizeObserver = new ResizeObserver(entries => {
      for (const entry of entries) {
        setContainerHeight(entry.contentRect.height)
      }
    })

    // Initial measurement
    setContainerHeight(container.clientHeight)
    resizeObserver.observe(container)

    return () => {
      resizeObserver.disconnect()
    }
  }, [])

  useEffect(() => {
    // Enable virtualization for large message lists (>100 messages)
    setIsVirtualized(messages.length > 100)

    if (isVirtualized) {
      const overscan = 5
      let rafId: number | null = null

      // Calculate visible range based on scroll position
      const calculateVisibleRange = () => {
        const container = messagesContainerRef.current
        if (!container || !containerHeight) return

        const scrollTop = container.scrollTop
        const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan)
        const visibleCount = Math.ceil(containerHeight / itemHeight) + overscan * 2
        const endIndex = Math.min(messages.length, startIndex + visibleCount)

        setRenderRange({ start: startIndex, end: endIndex })
      }

      const onScroll = () => {
        if (rafId !== null) return
        rafId = requestAnimationFrame(() => {
          rafId = null
          calculateVisibleRange()
        })
      }

      // Initial compute
      calculateVisibleRange()

      const container = messagesContainerRef.current
      if (container) {
        container.addEventListener('scroll', onScroll)
      }

      return () => {
        if (container) {
          container.removeEventListener('scroll', onScroll)
        }
        if (rafId !== null) cancelAnimationFrame(rafId)
      }
    } else {
      // For small lists, render all messages
      setRenderRange({ start: 0, end: messages.length })
    }
  }, [messages.length, isVirtualized, containerHeight, itemHeight])

  const renderVirtualizedMessages = () => {
    const { start, end } = renderRange
    const itemsToRender = messages.slice(start, end)
    
    return itemsToRender.map((message, index) => {
      const actualIndex = start + index
      return (
        <div
          key={message.id}
          style={{
            position: 'absolute',
            top: `${actualIndex * itemHeight}px`,
            width: '100%',
          }}
        >
          <MessageBubble
            message={message}
            onRegenerate={handleRegenerate}
            onPinMessage={togglePinMessage}
            currentContextId={selectedContextId}
          />
        </div>
      )
    })
  }

  if (!currentConversationId) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <div className="flex justify-center mb-6">
            <img src="/logo2_sq.svg" alt="ChatLo Logo" width="150" height="150" />
          </div>
          <h2 className="text-2xl font-semibold mb-3 text-supplement1">Gain Back Control with Local-first AI Chat</h2>
          <p className="text-gray-400 mb-8 max-w-md">
            Experience this Privacy-Focused yet Powerful AI Chatting!
          </p>
          <button
            onClick={() => {
              try {
                console.log('Starting new draft conversation from welcome screen...')
                const store = useAppStore.getState()
                const draftId = store.createDraftConversation('New Conversation')
                console.log('Created draft conversation:', draftId)
                store.setCurrentConversation(draftId)
                console.log('Welcome screen draft conversation setup complete')
              } catch (error) {
                console.error('Failed to create draft conversation from welcome:', error)
                alert('Failed to create conversation: ' + (error instanceof Error ? error.message : 'Unknown error'))
              }
            }}
            className="u1-button-primary"
          >
            Start New Conversation
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 flex flex-col h-full">
      {/* Chat Header with Context Vault Selector */}
      <header className="relative z-40 flex-shrink-0 flex items-center px-4 md:px-6 py-2 border-b border-tertiary/30 bg-gray-900/40 backdrop-blur-sm">
        {/* Left: Title and message count */}
        <div className="flex items-center gap-4 flex-1">
          <div className="flex items-center gap-2">
            <h2 className="text-sm font-medium text-supplement1 truncate">
              {currentConversation?.title || 'Conversation'}
            </h2>
            <span className="text-xs text-gray-500">
              • {messages.length}
            </span>
          </div>
        </div>

        {/* Right: Context Vault Selector and options */}
        <div className="flex items-center gap-4">
          {/* Context Vault Selector - positioned on the right */}
          <div className="min-w-[200px] max-w-[520px] h-8 flex items-center">
            <ContextVaultSelector
              selectedContextId={selectedContextId || undefined}
              onContextChange={handleContextSelect}
              variant="inline"
            />
          </div>

          {/* Options button */}
          <div className="flex items-center gap-2 h-8">
            {/* Notebook button for quick access to chat notes */}
            <button 
              className="u1-button-ghost h-8 w-8 flex items-center justify-center p-1.5 text-supplement2 hover:text-primary transition-colors" 
              title="View chat notes and annotations"
              onClick={() => {
                // TODO: Implement chat notes viewer modal/overlay
                console.log('[CHAT-AREA] 📚 Notebook button clicked - chat notes viewer not yet implemented')
              }}
            >
              <Icon name="book" className="text-xs" />
            </button>
            
            <button className="u1-button-ghost h-8 w-8 flex items-center justify-center p-1.5" title="More options">
              <Icon name="ellipsisVertical" className="text-xs" />
            </button>
          </div>
        </div>
      </header>

      {/* Debug Info Row */}
      <DebugInfoRow 
        selectedContextId={selectedContextId} 
        vaultRegistry={vaultRegistry}
        chatNotesLocation={chatNotesLocation}
      />

      {/* Messages */}
      <main
        ref={messagesContainerRef}
        className="flex-1 overflow-y-auto px-4 md:px-8 py-6 min-h-0"
      >
        <div className="max-w-4xl mx-auto">
          {messages.length === 0 ? (
            <div className="text-center py-12">
              {/* Debug info for empty state */}
              <div className="mb-6 p-3 bg-green-900/20 border border-green-500/30 rounded-lg">
                <div className="text-xs text-green-400 font-mono text-left max-w-2xl mx-auto">
                  <div className="flex items-center gap-4 mb-2">
                    <span>🔍 DEBUG INFO:</span>
                    <span>Context: {selectedContextId || 'None'}</span>
                    <span>|</span>
                    <span>Vaults: {vaultRegistry?.vaults?.length || 0}</span>
                  </div>
                  <div className="text-green-300">
                    Chat-notes will be stored in: {chatNotesLocation || 'Loading...'}
                  </div>
                  <div className="text-green-300 mt-1">
                    Vault Root: {vaultRegistry?.vaultRoot || 'mock-vaults'}
                  </div>
                </div>
              </div>
              
              <div className="h-12 w-12 rounded-full bg-gray-800 flex items-center justify-center mx-auto mb-4">
                <Bot className="h-6 w-6 text-gray-400" />
              </div>
              <p className="text-gray-400">
                No messages yet. Start the conversation!
              </p>
            </div>
          ) : isVirtualized ? (
            <div
              style={{
                position: 'relative',
                height: `${messages.length * itemHeight}px`,
              }}
            >
              {renderVirtualizedMessages()}
            </div>
          ) : (
            <div className="space-y-6">
              {messages.map((message) => (
                <MessageBubble
                  key={message.id}
                  message={message}
                  onRegenerate={handleRegenerate}
                  onPinMessage={togglePinMessage}
                  currentContextId={selectedContextId}
                />
              ))}
            </div>
          )}

          {/* Streaming message */}
          {streamingMessage !== null && (
            <StreamingMessageBubble content={streamingMessage} />
          )}

          {/* Loading indicator (when not streaming) */}
          {isLoading && streamingMessage === null && (
            <div className="flex items-start gap-3">
              <div className="h-8 w-8 rounded-full bg-secondary flex items-center justify-center">
                <Bot className="h-4 w-4 text-white" />
              </div>
              <div className="max-w-md">
                <div className="bg-gray-800 rounded-lg px-4 py-2">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                    <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                  </div>
                </div>
              </div>
            </div>
          )}
           
          <div ref={messagesEndRef} />
        </div>
      </main>

      {/* Input Area */}
      <div className="flex-shrink-0">
        <InputArea />
      </div>
    </div>
  )
}

export default ChatArea


