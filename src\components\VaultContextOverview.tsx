import React, { useState, useEffect } from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { ICONS } from './Icons/index'
import { ContextVaultCard } from '../types'
import { useNavigate } from 'react-router-dom'

interface VaultContextOverviewProps {
  context: ContextVaultCard
  onClose: () => void
  onNavigateToChat: (contextId: string) => void
  onNavigateToFiles: () => void
}

interface ChatItem {
  id: string
  title: string
  lastMessage: string
  updatedAt: string
  messageCount: number
}

interface FileItem {
  name: string
  path: string
  type: string
  size: number
  modified: string
  icon: any
}

const VaultContextOverview: React.FC<VaultContextOverviewProps> = ({
  context,
  onClose,
  // onNavigateToChat,
  // onNavigateToFiles
}) => {
  const navigate = useNavigate()
  const [recentChats, setRecentChats] = useState<ChatItem[]>([])
  const [recentFiles, setRecentFiles] = useState<FileItem[]>([])
  const [masterContent, setMasterContent] = useState<string>('')
  const [loading, setLoading] = useState(true)

  // Load live data when component mounts
  useEffect(() => {
    loadVaultData()
  }, [context.id])

  const loadVaultData = async () => {
    try {
      setLoading(true)
      
      // Load recent chats filtered by context
      await loadRecentChats()
      
      // Load recent files from vault
      await loadRecentFiles()
      
      // Load master.md content
      await loadMasterContent()
      
    } catch (error) {
      console.error('Error loading vault data:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadRecentChats = async () => {
    try {
      // Get all conversations and filter by context (placeholder logic)
      const response = await window.electronAPI.db.getConversations()
      
      // Handle both direct array response and wrapped response for backward compatibility
      const conversations = Array.isArray(response) ? response : (response?.success ? response.data || [] : [])
      
      // For now, show recent conversations (will be filtered by context in future)
      const recentConversations = conversations.slice(0, 10).map(conv => ({
        id: conv.id,
        title: conv.title || 'Untitled Chat',
        lastMessage: 'Recent conversation...',
        updatedAt: conv.updated_at,
        messageCount: 0 // Will be populated with actual message count
      }))
      
      setRecentChats(recentConversations)
    } catch (error) {
      console.error('Error loading recent chats:', error)
      setRecentChats([])
    }
  }

  const loadRecentFiles = async () => {
    try {
      // Get files from the vault directory
      if (context.path && window.electronAPI?.vault?.readDirectory) {
        const result = await window.electronAPI.vault.readDirectory(context.path)
        
        if (result.success && result.items) {
          const files = result.items
            .filter(item => !item.isDirectory && !item.name.startsWith('.'))
            .slice(0, 10)
            .map(item => ({
              name: item.name,
              path: item.path,
              type: getFileType(item.name),
              size: item.size || 0,
              modified: item.modified || new Date().toISOString(),
              icon: getFileIcon(item.name)
            }))
          
          setRecentFiles(files)
        }
      }
    } catch (error) {
      console.error('Error loading recent files:', error)
      setRecentFiles([])
    }
  }

  const loadMasterContent = async () => {
    try {
      if (context.path && window.electronAPI?.vault?.readFile) {
        const masterPath = `${context.path}/master.md`
        const result = await window.electronAPI.vault.readFile(masterPath)
        
        if (result.success && result.content) {
          setMasterContent(result.content)
        } else {
          // Create default master.md content
          setMasterContent(`# ${context.name}\n\n${context.description}\n\n## Overview\n\nThis context vault contains your files and conversations related to ${context.name}.`)
        }
      }
    } catch (error) {
      console.error('Error loading master.md:', error)
      setMasterContent(`# ${context.name}\n\n${context.description}`)
    }
  }

  const getFileIcon = (filename: string) => {
    const ext = filename.split('.').pop()?.toLowerCase()
    switch (ext) {
      case 'md':
      case 'txt':
        return ICONS.fileText
      case 'js':
      case 'ts':
      case 'jsx':
      case 'tsx':
      case 'json':
        return ICONS.fileCode
      case 'png':
      case 'jpg':
      case 'jpeg':
      case 'gif':
      case 'svg':
        return ICONS.fileImage
      case 'pdf':
        return ICONS.filePdf
      default:
        return ICONS.file
    }
  }

  const getFileType = (filename: string) => {
    const ext = filename.split('.').pop()?.toLowerCase()
    return ext || 'file'
  }

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    const diffDays = Math.floor(diffHours / 24)
    
    if (diffHours < 1) return 'Just now'
    if (diffHours < 24) return `${diffHours}h ago`
    if (diffDays < 7) return `${diffDays}d ago`
    return date.toLocaleDateString()
  }

  const handleChatClick = (chatId: string) => {
    navigate(`/chat/${chatId}?context=${context.id}`)
    onClose()
  }

  const handleNewChat = () => {
    navigate(`/chat?context=${context.id}`)
    onClose()
  }

  const handleFileClick = (file: FileItem) => {
    navigate(`/files?context=${context.id}&file=${encodeURIComponent(file.path)}`)
    onClose()
  }

  const handleAddFiles = async () => {
    try {
      if (window.electronAPI?.files?.showOpenDialog) {
        const result = await window.electronAPI.files.showOpenDialog({
          title: 'Select Files to Add',
          properties: ['openFile', 'multiSelections'],
          filters: [
            { name: 'All Files', extensions: ['*'] },
            { name: 'Documents', extensions: ['pdf', 'doc', 'docx', 'txt', 'md'] },
            { name: 'Images', extensions: ['jpg', 'jpeg', 'png', 'gif', 'webp'] }
          ]
        })
        
        if (result.success && result.filePaths && result.filePaths.length > 0) {
          // Handle file upload to vault
          console.log('Files selected for upload:', result.filePaths)
          // Refresh file list after upload
          await loadRecentFiles()
        }
      }
    } catch (error) {
      console.error('Error adding files:', error)
    }
  }

  const handleEditMaster = () => {
    navigate(`/files?context=${context.id}&mode=master`)
    onClose()
  }

  const renderMarkdown = (content: string) => {
    // Simple markdown rendering for preview
    return content
      .replace(/^# (.*$)/gm, '<h1 class="text-lg font-semibold text-supplement1 mb-2">$1</h1>')
      .replace(/^## (.*$)/gm, '<h2 class="text-base font-medium text-supplement1 mb-2">$1</h2>')
      .replace(/^### (.*$)/gm, '<h3 class="text-sm font-medium text-supplement1 mb-1">$1</h3>')
      .replace(/\n\n/g, '</p><p class="text-sm text-gray-300 mb-2">')
      .replace(/^(.*)$/gm, '<p class="text-sm text-gray-300 mb-2">$1</p>')
  }

  return (
    <div className="fixed inset-0 bg-gray-800/80 backdrop-blur-sm z-50" onClick={onClose}>
      <div className="flex items-center justify-center min-h-screen p-6">
        <div className="bg-gray-800 rounded-xl border border-tertiary/50 w-full max-w-6xl max-h-[80vh] overflow-hidden" onClick={(e) => e.stopPropagation()}>
          {/* Enhanced Header */}
          <div className="p-6 border-b border-tertiary/50">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <FontAwesomeIcon icon={ICONS.folder} className="text-supplement2 text-lg" />
                <div>
                  <h2 className="text-lg font-semibold text-supplement1">{context.name}</h2>
                  <p className="text-sm text-gray-400">{context.description}</p>
                </div>
              </div>
              <button
                onClick={onClose}
                className="p-2 hover:bg-gray-700 rounded-lg transition-colors"
              >
                <FontAwesomeIcon icon={ICONS.xmark} className="text-gray-400" />
              </button>
            </div>
            
            {/* Stats Bar */}
            <div className="flex items-center gap-6 mt-4 text-sm text-gray-400">
              <span>Files: {context.fileCount}</span>
              <span>Chats: {recentChats.length}</span>
              <span>Last activity: {formatTimeAgo(context.lastActivity)}</span>
            </div>
          </div>

          {/* 3-Panel Content */}
          <div className="flex h-[calc(80vh-140px)]">
            {/* Left: Recent Chats (33%) */}
            <div className="w-1/3 flex flex-col border-r border-tertiary/50">
              <div className="p-4 border-b border-tertiary/50">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="font-medium text-supplement1">Recent Chats</h3>
                </div>
                <button 
                  onClick={handleNewChat}
                  className="w-full p-2 bg-primary/20 text-primary rounded-lg text-sm hover:bg-primary/30 transition-colors flex items-center justify-center gap-2"
                >
                  <FontAwesomeIcon icon={ICONS.plus} className="text-xs" />
                  New Chat
                </button>
              </div>
              <div className="flex-1 overflow-y-auto p-4">
                {loading ? (
                  <div className="text-center text-gray-400 text-sm">Loading chats...</div>
                ) : recentChats.length > 0 ? (
                  <div className="space-y-2">
                    {recentChats.map((chat) => (
                      <div
                        key={chat.id}
                        onClick={() => handleChatClick(chat.id)}
                        className="p-3 bg-gray-700/50 rounded-lg hover:bg-gray-700 cursor-pointer transition-colors"
                      >
                        <div className="flex items-start gap-2">
                          <FontAwesomeIcon icon={ICONS.comment} className="text-primary text-xs mt-1" />
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-supplement1 truncate">{chat.title}</p>
                            <p className="text-xs text-gray-400 mt-1">{formatTimeAgo(chat.updatedAt)}</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center text-gray-400 text-sm">No recent chats</div>
                )}
              </div>
            </div>

            {/* Middle: Recent Files (33%) */}
            <div className="w-1/3 flex flex-col border-r border-tertiary/50">
              <div className="p-4 border-b border-tertiary/50">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="font-medium text-supplement1">Recent Files</h3>
                </div>
                <button 
                  onClick={handleAddFiles}
                  className="w-full p-2 bg-primary/20 text-primary rounded-lg text-sm hover:bg-primary/30 transition-colors flex items-center justify-center gap-2"
                >
                  <FontAwesomeIcon icon={ICONS.plus} className="text-xs" />
                  Add Files
                </button>
              </div>
              <div className="flex-1 overflow-y-auto p-4">
                {loading ? (
                  <div className="text-center text-gray-400 text-sm">Loading files...</div>
                ) : recentFiles.length > 0 ? (
                  <div className="space-y-2">
                    {recentFiles.map((file, index) => (
                      <div
                        key={index}
                        onClick={() => handleFileClick(file)}
                        className="flex items-center gap-3 p-2 hover:bg-gray-700/50 rounded-lg cursor-pointer transition-colors"
                      >
                        <FontAwesomeIcon icon={file.icon || ICONS.file} className="text-supplement2 text-sm" />
                        <div className="flex-1 min-w-0">
                          <p className="text-sm text-supplement1 truncate">{file.name}</p>
                          <p className="text-xs text-gray-400">{formatTimeAgo(file.modified)}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center text-gray-400 text-sm">No files yet</div>
                )}
              </div>
            </div>

            {/* Right: Master.md Preview (34%) */}
            <div className="w-1/3 flex flex-col">
              <div className="p-4 border-b border-tertiary/50">
                <div className="flex items-center justify-between">
                  <h3 className="font-medium text-supplement1">Master.md Preview</h3>
                  <button
                    onClick={handleEditMaster}
                    className="p-1 hover:bg-gray-700 rounded transition-colors"
                  >
                    <FontAwesomeIcon icon={ICONS.edit} className="text-gray-400 text-sm" />
                  </button>
                </div>
              </div>
              <div className="flex-1 overflow-y-auto p-4">
                {loading ? (
                  <div className="text-center text-gray-400 text-sm">Loading master.md...</div>
                ) : (
                  <div 
                    className="prose prose-invert prose-sm max-w-none"
                    dangerouslySetInnerHTML={{ __html: renderMarkdown(masterContent) }}
                  />
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default VaultContextOverview
