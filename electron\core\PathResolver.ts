import * as path from 'path'
import * as fs from 'fs'

export interface UnifiedPathResolution {
  type: 'file' | 'annotation' | 'vault-relative'
  vault: string
  context: string
  path: string
  originalPath: string
  isValid: boolean
}

/**
 * PathResolver
 * Centralized, platform-aware path helpers for core services
 * UNIFIED SYSTEM: Handles all path types (files, annotations, vault-relative)
 * Replaces the fragmented isVirtualPath system
 */
export class PathResolver {
  static normalizePath(targetPath: string): string {
    // SECURITY FIX: Validate input before normalization
    if (typeof targetPath !== 'string') {
      console.error('[SECURITY] 🚨 Non-string input to normalizePath:', targetPath)
      throw new Error('Invalid path input type')
    }
    
    // Detect corrupted drive letter patterns
    if (/^[A-Z]:_/.test(targetPath)) {
      console.error('[SECURITY] 🚨 Corrupted drive letter pattern detected:', targetPath)
      throw new Error(`Corrupted drive letter pattern: ${targetPath}`)
    }
    
    // Relaxed: Allow absolute paths during save-testing phase
    // (Your rule: minimize security until save flow is proven; we can re-tighten later.)
    return path.normalize(targetPath)
  }

  static joinSafe(...parts: string[]): string {
    // SECURITY FIX: Prevent path injection attacks while allowing a legitimate absolute vault path as the first segment
    const sanitizedParts = parts.map((part, index) => {
      if (typeof part !== 'string') {
        console.error('[SECURITY] 🚨 Non-string part in path join:', part)
        throw new Error('Invalid path part type')
      }

      // Relaxed: Allow absolute Windows path as the first segment (save-testing phase)
      const isWindowsAbsolute = /^[A-Z]:\\/.test(part)
      const isUnixStyleWindowsAbs = /^\/[A-Z]:\//.test(part)
      if ((isWindowsAbsolute || isUnixStyleWindowsAbs) && index === 0) {
        return part
      }

      // Detect and block other suspicious path patterns
      const suspiciousPatterns = [
        /^[A-Z]:_/, // C_, D_, etc. - corrupted drive letters
        /\.\./, // Directory traversal
        /\\\\/, // Double backslashes
        /\/\//, // Double forward slashes
      ]

      for (const pattern of suspiciousPatterns) {
        if (pattern.test(part)) {
          console.error('[SECURITY] 🚨 Suspicious path pattern detected:', {
            part,
            pattern: pattern.source,
            stack: new Error().stack
          })
          throw new Error(`Suspicious path pattern: ${part}`)
        }
      }

      return part
    })

    return path.join(...sanitizedParts)
  }

  static getContextDir(vaultPath: string): string {
    const normalizedVault = this.normalizePath(vaultPath)
    // Legacy method - prefer .intelligence but fallback to .context for compatibility
    return this.joinSafe(normalizedVault, '.intelligence')
  }

  static getContextFilesDir(vaultPath: string): string {
    return this.joinSafe(this.getContextDir(vaultPath), 'files')
  }

  static getFileNameWithoutExtension(filePath: string): string {
    const fileName = path.basename(filePath)
    const lastDotIndex = fileName.lastIndexOf('.')
    return lastDotIndex > 0 ? fileName.substring(0, lastDotIndex) : fileName
  }

  // Stable short hash used across the app for mapping a file to its metadata
  static computeStableHash(input: string): string {
    // Simple hash for now - can be improved with crypto if needed
    let hash = 0
    for (let i = 0; i < input.length; i++) {
      const char = input.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Convert to 32-bit integer
    }
    
    // Extract filename for better identification
    const fileName = path.basename(input)
    const hashSuffix = Math.abs(hash).toString(36).substring(0, 8)
    
    return `${fileName}_${hashSuffix}`
  }

  static getIntelligenceJsonPath(filePath: string, vaultPath: string): { filesDir: string; jsonPath: string; fileHash: string } {
    // SECURITY FIX: Validate paths before processing
    if (!this.validateVaultPathBasic(vaultPath)) {
      console.error('[SECURITY] 🚨 Invalid vault path in getIntelligenceJsonPath:', vaultPath)
      console.error('[SECURITY] 🚨 This might be a context ID instead of a full vault path')
      console.error('[SECURITY] 🚨 Expected format: C:\\Users\\<USER>\\vault-name\\context-name')
      console.error('[SECURITY] 🚨 Received: ', vaultPath)
      throw new Error(`Invalid vault path: ${vaultPath} - Expected full vault path, got context ID`)
    }
    
    const normalizedFile = this.normalizePath(filePath)
    const normalizedVault = this.normalizePath(vaultPath)

    const fileHash = this.computeStableHash(normalizedFile)

    // CRITICAL FIX: Handle context-notes directory structure
    {
      const normalizedSlash = normalizedFile.replace(/\\/g, '/');
      if (normalizedSlash.startsWith('context-notes/')) {
        // Special handling for context notes - use direct path under .intelligence
        const intelligenceDir = this.getIntelligenceBaseDir(normalizedVault)
        const contextNotesDir = this.joinSafe(intelligenceDir, 'context-notes')
        const fileName = normalizedSlash.replace('context-notes/', '')
        const jsonPath = this.joinSafe(contextNotesDir, fileName)

        console.log('[IPC-LOG] 📝 PathResolver context notes path:', {
          originalPath: filePath,
          normalizedFile,
          vaultPath: normalizedVault,
          intelligenceDir: this.getIntelligenceBaseDir(normalizedVault),
          contextNotesDir,
          fileName,
          jsonPath,
          timestamp: new Date().toISOString()
        });

        return { filesDir: contextNotesDir, jsonPath, fileHash }
      }
    }

    // SAFETY SHIM (V03): Handle callers mistakenly passing ".intelligence/context-notes/<file>.json" in filePath
    // Normalize to relative context-notes mode to avoid misclassification and root escapes
    {
      const normalizedSlash = normalizedFile.replace(/\\/g, '/');
      if (normalizedSlash.includes('/.intelligence/context-notes/')) {
        const intelligenceDir = this.getIntelligenceBaseDir(normalizedVault)
        const contextNotesDir = this.joinSafe(intelligenceDir, 'context-notes')
        const fileName = path.basename(normalizedFile)
        const jsonPath = this.joinSafe(contextNotesDir, fileName)

        console.log('[IPC-LOG] 🛡️ PathResolver shim normalized filePath with .intelligence/context-notes:', {
          originalPath: filePath,
          normalizedFile,
          vaultPath: normalizedVault,
          contextNotesDir,
          fileName,
          jsonPath,
          timestamp: new Date().toISOString()
        });

        return { filesDir: contextNotesDir, jsonPath, fileHash }
      }
    }

    // LEGACY: Keep old structure for backward compatibility with intelligence:read/write
    // Note: V02 sessions use getIntelligenceSessionsDir() instead
    const filesDir = this.joinSafe(this.getIntelligenceBaseDir(normalizedVault), 'files')
    const jsonPath = this.joinSafe(filesDir, `${fileHash}.json`)

    return { filesDir, jsonPath, fileHash }
  }

  // Intelligence V02 storage layout: <vault>/.intelligence/documents/<hash>/sessions/
  static getIntelligenceBaseDir(vaultPath: string): string {
    // SECURITY FIX: Validate vault path before creating intelligence directory
    if (!this.validateVaultPathBasic(vaultPath)) {
      console.error('[SECURITY] 🚨 Invalid vault path in getIntelligenceBaseDir:', vaultPath)
      throw new Error(`Invalid vault path: ${vaultPath}`)
    }
    
    return this.joinSafe(this.normalizePath(vaultPath), '.intelligence')
  }

  static getIntelligenceDocumentDir(filePath: string, vaultPath: string): string {
    const fileHash = this.computeStableHash(filePath)
    return this.joinSafe(this.getIntelligenceBaseDir(vaultPath), 'documents', fileHash)
  }

  static getIntelligenceSessionsDir(filePath: string, vaultPath: string): string {
    return this.joinSafe(this.getIntelligenceDocumentDir(filePath, vaultPath), 'sessions')
  }

  // Artifacts persistence contract
  static getArtifactsDir(filePath: string, vaultPath: string): string {
    return this.joinSafe(this.getIntelligenceDocumentDir(filePath, vaultPath), 'artifacts')
  }

  static getArtifactsPath(filePath: string, vaultPath: string): string {
    return this.joinSafe(this.getArtifactsDir(filePath, vaultPath), 'artifacts.json')
  }

  // SECURITY ENHANCEMENT: Validate vault paths before use (basic validation)
  static validateVaultPathBasic(vaultPath: string): boolean {
    try {
      if (typeof vaultPath !== 'string') {
        console.error('[SECURITY] 🚨 Invalid vault path type:', typeof vaultPath)
        return false
      }
      
      // Check for corrupted drive letters
      if (/^[A-Z]:_/.test(vaultPath)) {
        console.error('[SECURITY] 🚨 Corrupted drive letter in vault path:', vaultPath)
        return false
      }
      
      // Check for path injection patterns
      if (vaultPath.includes('..') || vaultPath.includes('\\\\') || vaultPath.includes('//')) {
        console.error('[SECURITY] 🚨 Path injection pattern in vault path:', vaultPath)
        return false
      }
      
      // Relaxed: Allow absolute vault paths during save-testing phase
      return true
    } catch (error) {
      console.error('[SECURITY] ❌ Error validating vault path:', error)
      return false
    }
  }

  // SECURITY ENHANCEMENT: Detect and prevent codebase contamination
  static isCodebasePath(filePath: string): boolean {
    try {
      const normalized = this.normalizePath(filePath)
      const suspiciousPatterns = [
        /node_modules/i,
        /\.git/i,
        /src\//i,
        /electron\//i,
        /dist\//i,
        /package\.json/i,
        /tsconfig\.json/i,
        /vite\.config\.ts/i
      ]

      for (const pattern of suspiciousPatterns) {
        if (pattern.test(normalized)) {
          console.error('[SECURITY] 🚨 Codebase contamination detected:', {
            filePath: normalized,
            pattern: pattern.source
          })
          return true
        }
      }

      return false
    } catch (error) {
      console.error('[SECURITY] ❌ Error checking codebase path:', error)
      return true // Fail safe - reject if we can't verify
    }
  }

  // Infer the vault/context directory from an absolute file path
  // Strategy:
  // 1) If path contains '/documents/', take everything before that token as vaultPath
  // 2) Otherwise, walk up to find a directory containing a '.intelligence' folder (or '.context' for legacy)
  // 3) If neither works, return null
  // SECURITY ENHANCEMENT: Added strict boundary validation
  static inferVaultPath(filePath: string, options?: { allowedVaultRoots?: string[] }): string | null {
    try {
      // SECURITY CHECK: Prevent codebase contamination
      if (this.isCodebasePath(filePath)) {
        console.error('[SECURITY] 🚨 Rejecting codebase path:', filePath)
        return null
      }

      const normalizeSlashes = (p: string) => this.normalizePath(p).replace(/\\/g, '/')
      
      // SECURITY ENHANCEMENT: Strict boundary validation
      const withinAllowed = (candidate: string) => {
        if (!options?.allowedVaultRoots || options.allowedVaultRoots.length === 0) {
          console.warn('[SECURITY] ⚠️ No allowed vault roots specified, rejecting candidate:', candidate)
          return false
        }
        
        const cand = normalizeSlashes(candidate)
        const isWithin = options.allowedVaultRoots.some(root => {
          const normalizedRoot = normalizeSlashes(root)
          return cand.startsWith(normalizedRoot)
        })
        
        if (!isWithin) {
          console.error('[SECURITY] ❌ Candidate path outside allowed boundaries:', {
            candidate: cand,
            allowedRoots: options.allowedVaultRoots.map(r => normalizeSlashes(r))
          })
        }
        
        return isWithin
      }

      const normalized = normalizeSlashes(filePath)
      const token = '/documents/'
      const idx = normalized.lastIndexOf(token)
      if (idx !== -1) {
        const candidate = normalized.substring(0, idx)
        if (withinAllowed(candidate)) {
          const result = this.normalizePath(candidate)
          console.log('[SECURITY] ✅ Vault path inferred from documents token:', result)
          return result
        }
        return null
      }

      // Walk up to find a directory containing '.intelligence' (or fallback to '.context' for legacy)
      let current = path.dirname(this.normalizePath(filePath))
      const root = path.parse(current).root
      while (current && current !== root) {
        const intelligenceDir = this.joinSafe(current, '.intelligence')
        const contextDir = this.joinSafe(current, '.context')
        
        if (fs.existsSync(intelligenceDir) && fs.lstatSync(intelligenceDir).isDirectory()) {
          if (withinAllowed(current)) {
            const result = this.normalizePath(current)
            console.log('[SECURITY] ✅ Vault path inferred from .intelligence directory:', result)
            return result
          }
          return null
        }
        
        // Legacy fallback for existing vaults
        if (fs.existsSync(contextDir) && fs.lstatSync(contextDir).isDirectory()) {
          if (withinAllowed(current)) {
            const result = this.normalizePath(current)
            console.log('[SECURITY] ✅ Vault path inferred from .context directory:', result)
            return result
          }
          return null
        }
        
        current = path.dirname(current)
      }
      
      console.warn('[SECURITY] ⚠️ Could not infer vault path for:', filePath)
      return null
    } catch (error) {
      console.error('[SECURITY] ❌ Error in inferVaultPath:', error)
      return null
    }
  }

  // Relative path helper
  static getRelativePath(basePath: string, absolutePath: string): string {
    return path.relative(this.normalizePath(basePath), this.normalizePath(absolutePath))
  }

  /**
   * UNIFIED PATH RESOLUTION - Replaces all isVirtualPath logic
   * Handles all path types in a single, consistent interface
   */
  static resolveUnified(inputPath: string, options?: {
    vault?: string
    context?: string
    allowedVaultRoots?: string[]
  }): UnifiedPathResolution {
    try {
      // Determine path type
      const isAnnotationPath = inputPath.startsWith('chat-notes/')
      const isAbsolutePath = /^[A-Z]:\\/.test(inputPath) || inputPath.startsWith('/')

      if (isAnnotationPath) {
        return this.resolveAnnotationPath(inputPath, options)
      } else if (isAbsolutePath) {
        return this.resolveFilePath(inputPath, options)
      } else {
        return this.resolveVaultRelativePath(inputPath, options)
      }
    } catch (error) {
      console.error('[PathResolver] Failed to resolve path:', inputPath, error)
      return {
        type: 'file',
        vault: '',
        context: '',
        path: inputPath,
        originalPath: inputPath,
        isValid: false
      }
    }
  }

  /**
   * Resolve annotation paths (chat-notes/...)
   */
  private static resolveAnnotationPath(inputPath: string, options?: {
    vault?: string
    context?: string
  }): UnifiedPathResolution {
    const vault = options?.vault || ''
    const context = options?.context || ''

    if (!vault || !context) {
      throw new Error('Annotation paths require vault and context')
    }

    // Convert chat-notes/file.json → .intelligence/context-notes/file.json
    const fileName = inputPath.replace('chat-notes/', '')
    const resolvedPath = this.joinSafe(vault, context, '.intelligence', 'context-notes', fileName)

    return {
      type: 'annotation',
      vault,
      context,
      path: resolvedPath,
      originalPath: inputPath,
      isValid: true
    }
  }

  /**
   * Resolve absolute file paths
   */
  private static resolveFilePath(inputPath: string, options?: {
    allowedVaultRoots?: string[]
  }): UnifiedPathResolution {
    const allowedRoots = options?.allowedVaultRoots || []
    const inferredVault = this.inferVaultPath(inputPath, { allowedVaultRoots: allowedRoots })

    if (!inferredVault) {
      throw new Error(`Cannot infer vault for file path: ${inputPath}`)
    }

    // Extract context from path
    const contextId = this.extractContextFromPath(inputPath, inferredVault)

    return {
      type: 'file',
      vault: inferredVault,
      context: contextId,
      path: this.normalizePath(inputPath),
      originalPath: inputPath,
      isValid: true
    }
  }

  /**
   * Resolve vault-relative paths (documents/file.pdf)
   */
  private static resolveVaultRelativePath(inputPath: string, options?: {
    vault?: string
    context?: string
  }): UnifiedPathResolution {
    const vault = options?.vault || ''
    const context = options?.context || ''

    if (!vault || !context) {
      throw new Error('Vault-relative paths require vault and context')
    }

    const resolvedPath = this.joinSafe(vault, context, inputPath)

    return {
      type: 'vault-relative',
      vault,
      context,
      path: resolvedPath,
      originalPath: inputPath,
      isValid: true
    }
  }

  /**
   * Extract context ID from file path
   */
  private static extractContextFromPath(filePath: string, vaultPath: string): string {
    const relativePath = filePath.replace(vaultPath, '').replace(/^[\\\/]/, '')
    const pathParts = relativePath.split(/[\\\/]/)
    return pathParts[0] || 'default'
  }

  // Generate a unique filename in a directory by appending (n) before the extension
  static async generateUniqueFilename(dir: string, desiredName: string): Promise<string> {
    const normalizedDir = this.normalizePath(dir)
    const ext = path.extname(desiredName)
    const nameWithoutExt = path.basename(desiredName, ext)
    let candidate = desiredName
    let counter = 1
    while (true) {
      const full = this.joinSafe(normalizedDir, candidate)
      const exists = await fs.promises.access(full).then(() => true).catch(() => false)
      if (!exists) return candidate
      candidate = `${nameWithoutExt} (${counter})${ext}`
      counter += 1
    }
  }

  /**
   * SECURITY ENHANCEMENT: Comprehensive path sanitization and validation
   * Prevents path injection, directory traversal, and ensures vault boundary compliance
   */
  static sanitizeAndValidatePath(inputPath: string, allowedVaultRoots: string[]): string | null {
    try {
      // CODING QUALITY FIX: Don't remove drive letters for legitimate vault paths
      // Only sanitize if the path is actually malicious
      
      // Check if this is a legitimate vault path first
      const isLegitimateVaultPath = allowedVaultRoots.some(root => {
        const normalizedRoot = this.normalizePath(root).replace(/\\/g, '/');
        const normalizedInput = this.normalizePath(inputPath).replace(/\\/g, '/');
        return normalizedInput.startsWith(normalizedRoot);
      });
      
      let sanitizedPath = inputPath;
      
      if (!isLegitimateVaultPath) {
        // Only sanitize non-legitimate paths
        sanitizedPath = inputPath.replace(/^[A-Z]:\\/, '').replace(/^\/[A-Z]:\//, '');
        
        // SECURITY: Ensure path is relative and within vault boundaries
        if (sanitizedPath.includes('..') || sanitizedPath.startsWith('/') || sanitizedPath.startsWith('\\')) {
          console.error('[SECURITY] 🚨 Rejecting path with directory traversal:', inputPath);
          return null;
        }
      }
      
      // SECURITY: Remove any null bytes or control characters
      sanitizedPath = sanitizedPath.replace(/[\x00-\x1f\x7f]/g, '');
      
      // SECURITY: Validate against allowed vault roots
      if (!this.validateVaultPath(sanitizedPath, allowedVaultRoots)) {
        console.error('[SECURITY] 🚨 Path outside allowed vault boundaries:', sanitizedPath);
        return null;
      }
      
      console.log('[SECURITY] ✅ Path sanitized and validated:', sanitizedPath);
      return sanitizedPath;
    } catch (error) {
      console.error('[SECURITY] ❌ Error in path sanitization:', error);
      return null;
    }
  }

  /**
   * SECURITY ENHANCEMENT: Strict vault path validation
   * Ensures all paths are within allowed vault boundaries
   */
  static validateVaultPath(candidatePath: string, allowedVaultRoots: string[]): boolean {
    try {
      if (!allowedVaultRoots || allowedVaultRoots.length === 0) {
        console.error('[SECURITY] 🚨 No allowed vault roots specified');
        return false;
      }

      const normalizedCandidate = this.normalizePath(candidatePath);

      // RELAXED PHASE: If the candidate is within allowed roots, accept immediately
      const earlyAllowed = allowedVaultRoots.some(root => normalizedCandidate.startsWith(this.normalizePath(root)));
      if (earlyAllowed) {
        return true;
      }

      // SECURITY: Check if path contains any suspicious patterns
      const suspiciousPatterns = [
        // Temporarily allow Windows drive letters while in save-testing phase
        // /^[A-Z]:\\/,
        /^\/[A-Z]:\//, // Unix-style Windows paths
        /\\\\/, // Double backslashes
        /undefined/, // undefined references
        /null/, // null references
      ];

      // SECURITY: Allow legitimate system paths while blocking malicious ones
      const legitimateSystemPaths = [
        'shared-dropbox',
        'personal-vault', 
        'work-vault',
        'getting-started',
        'documents',
        'images',
        'artifacts',
        '.intelligence',
        '.context',
        'master.md',
        'ChatLo_Vaults',
        'Post-Kernel-Test'  // Allow Post-Kernel-Test3, Post-Kernel-Test4, etc.
      ];

      // Check if the path contains legitimate system paths
      const containsLegitimatePath = legitimateSystemPaths.some(legitPath => 
        normalizedCandidate.includes(legitPath)
      );

      // If it contains legitimate paths, be more lenient with user directory checks
      if (containsLegitimatePath) {
        // Only block malicious user directory patterns, not legitimate ones
        if (normalizedCandidate.includes('C:\\Users\\<USER>\\Users\\'
          });
          return false;
        }
      } else {
        // For paths without legitimate components, apply stricter checks
        suspiciousPatterns.push(/C:\\Users\\<USER>\\') || intelligenceData.file_path.startsWith('/')) {
          console.error('[SECURITY] 🚨 Absolute path detected in file_path:', intelligenceData.file_path);
          return false;
        }
        
        console.log('[SECURITY] ✅ Intelligence data validation passed (json format)');
        return true;
      } else if (data.rawMarkdown) {
        // Raw markdown format - used by UnifiedOrganizeService
        if (typeof data.rawMarkdown !== 'string') {
          console.error('[SECURITY] 🚨 Invalid rawMarkdown field type');
          return false;
        }
        
        console.log('[SECURITY] ✅ Intelligence data validation passed (rawMarkdown format)');
        return true;
      } else if (data.document_hash && data.file_path && data.storage_metadata) {
        // Legacy format: direct intelligence data
        if (data.file_path.includes(':\\') || data.file_path.startsWith('/')) {
          console.error('[SECURITY] 🚨 Absolute path detected in file_path:', data.file_path);
          return false;
        }
        
        console.log('[SECURITY] ✅ Intelligence data validation passed (legacy format)');
        return true;
      } else {
        // Check if it's a valid intelligence object with required fields
        if (data.file_path && typeof data.file_path === 'string') {
          // Validate file_path is not an absolute path
          if (data.file_path.includes(':\\') || data.file_path.startsWith('/')) {
            console.error('[SECURITY] 🚨 Absolute path detected in file_path:', data.file_path);
            return false;
          }
          
          console.log('[SECURITY] ✅ Intelligence data validation passed (direct format)');
          return true;
        }
        
        console.error('[SECURITY] 🚨 Invalid intelligence data format - missing required fields');
        return false;
      }
    } catch (error) {
      console.error('[SECURITY] ❌ Error in data validation:', error);
      return false;
    }
  }

  /**
   * SECURITY ENHANCEMENT: Emergency cleanup and quarantine
   * Identifies and quarantines corrupted or malicious files
   */
  static async quarantineCorruptedFiles(vaultRoot: string): Promise<{ quarantined: number, errors: number }> {
    let quarantined = 0;
    let errors = 0;
    
    try {
      console.log('[SECURITY] 🚨 Starting emergency security cleanup...');
      
      // Check for undefined/.context/ folder (critical security breach)
      const undefinedContextPath = path.join(vaultRoot, 'undefined', '.context');
      if (fs.existsSync(undefinedContextPath)) {
        console.error('[SECURITY] 🚨 CRITICAL: Found undefined/.context/ folder - immediate quarantine required');
        
        // Move to quarantine folder
        const quarantinePath = path.join(vaultRoot, '.quarantine', 'corrupted_' + Date.now());
        await fs.promises.mkdir(path.dirname(quarantinePath), { recursive: true });
        await fs.promises.rename(undefinedContextPath, quarantinePath);
        
        console.log('[SECURITY] ✅ Quarantined corrupted undefined/.context/ folder to:', quarantinePath);
        quarantined++;
      }
      
      // CRITICAL: Check for .intelligence folder in project root (security breach)
      const projectRoot = process.cwd();
      const rootIntelligencePath = path.join(projectRoot, '.intelligence');
      if (fs.existsSync(rootIntelligencePath)) {
        console.error('[SECURITY] 🚨 CRITICAL: Found .intelligence folder in project root - immediate quarantine required');
        
        const quarantinePath = path.join(projectRoot, '.quarantine', 'root_intelligence_breach_' + Date.now());
        await fs.promises.mkdir(path.dirname(quarantinePath), { recursive: true });
        await fs.promises.rename(rootIntelligencePath, quarantinePath);
        
        console.log('[SECURITY] ✅ Quarantined root .intelligence folder to:', quarantinePath);
        quarantined++;
      }
      
      // CRITICAL: Check for C_ folder in project root (security breach)
      const rootCPath = path.join(projectRoot, 'C_');
      if (fs.existsSync(rootCPath)) {
        console.error('[SECURITY] 🚨 CRITICAL: Found C_ folder in project root - immediate quarantine required');
        
        const quarantinePath = path.join(projectRoot, '.quarantine', 'root_c_path_breach_' + Date.now());
        await fs.promises.mkdir(path.dirname(quarantinePath), { recursive: true });
        await fs.promises.rename(rootCPath, quarantinePath);
        
        console.log('[SECURITY] ✅ Quarantined root C_ folder to:', quarantinePath);
        quarantined++;
      }
      
      // Scan for other corrupted patterns
      const corruptedPatterns = [
        /^[A-Z]:\\/, // Windows absolute paths
        /^\/[A-Z]:\//, // Unix-style Windows paths
        /undefined/, // undefined references
        /null/, // null references
      ];
      
      // Scan intelligence folders for corrupted files
      const intelligenceFolders = [
        path.join(vaultRoot, '.intelligence'),
        path.join(vaultRoot, '.context')
      ];
      
      for (const intelFolder of intelligenceFolders) {
        if (fs.existsSync(intelFolder)) {
          await this.scanAndQuarantineCorruptedFiles(intelFolder, corruptedPatterns, vaultRoot);
        }
      }
      
      console.log('[SECURITY] ✅ Emergency security cleanup completed');
    } catch (error) {
      console.error('[SECURITY] ❌ Error during emergency cleanup:', error);
      errors++;
    }
    
    return { quarantined, errors };
  }

  /**
   * SECURITY ENHANCEMENT: Scan and quarantine corrupted files
   */
  private static async scanAndQuarantineCorruptedFiles(folderPath: string, patterns: RegExp[], vaultRoot: string): Promise<void> {
    try {
      const files = await fs.promises.readdir(folderPath, { withFileTypes: true });
      
      for (const file of files) {
        if (file.isFile() && file.name.endsWith('.json')) {
          const filePath = path.join(folderPath, file.name);
          
          try {
            const content = await fs.promises.readFile(filePath, 'utf8');
            const data = JSON.parse(content);
            
            // Check for corrupted patterns
            const isCorrupted = patterns.some(pattern => 
              JSON.stringify(data).match(pattern)
            );
            
            if (isCorrupted) {
              console.error('[SECURITY] 🚨 Corrupted file detected:', filePath);
              
              // Move to quarantine
              const quarantinePath = path.join(vaultRoot, '.quarantine', 'corrupted_' + Date.now() + '_' + file.name);
              await fs.promises.mkdir(path.dirname(quarantinePath), { recursive: true });
              await fs.promises.rename(filePath, quarantinePath);
              
              console.log('[SECURITY] ✅ Quarantined corrupted file to:', quarantinePath);
            }
          } catch (parseError) {
            console.error('[SECURITY] 🚨 Malformed JSON file detected:', filePath);
            
            // Move malformed files to quarantine
            const quarantinePath = path.join(vaultRoot, '.quarantine', 'malformed_' + Date.now() + '_' + file.name);
            await fs.promises.mkdir(path.dirname(quarantinePath), { recursive: true });
            await fs.promises.rename(filePath, quarantinePath);
            
            console.log('[SECURITY] ✅ Quarantined malformed file to:', quarantinePath);
          }
        }
      }
    } catch (error) {
      console.error('[SECURITY] ❌ Error scanning folder for corrupted files:', error);
    }
  }
}
