import React, { useState, useRef, useEffect } from 'react'
import { useAppStore } from '../store'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { ICONS } from './Icons/index'
import ChatSettingsDrawer from './ChatSettingsDrawer'
import AttachmentMenu from './AttachmentMenu'
import FilePicker from './FilePicker'
import FileAutocomplete from './FileAutocomplete'
import FileAttachments from './FileAttachments'
import { FileRecord } from '../types'
import { validateImageFile, validateImageDimensions, resizeImage, shouldOptimizeImage } from '../utils/imageUtils'
import { conversationIntelligenceManager } from '../services/conversationIntelligenceManager'
import { unifiedIntelligenceService } from '../services/unifiedIntelligenceService'
import { useArtifactToasts } from './artifacts/controls/ArtifactToast'
// import { CompactVaultSelector } from './CompactVaultSelector'
import { sharedDropboxService } from '../services/sharedDropboxService'
import { vaultFileHandler } from '../services/vaultFileHandler'



// Helper function to convert file to base64 safely
const fileToBase64 = async (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = () => {
      const result = reader.result as string
      // Remove data URL prefix to get just the base64 string
      const base64 = result.split(',')[1]
      resolve(base64)
    }
    reader.onerror = () => reject(new Error('Failed to read file'))
    reader.readAsDataURL(file)
  })
}

const InputArea: React.FC = () => {
  const { currentConversationId, sendMessage, isLoading, models, settings } = useAppStore()
  const toasts = useArtifactToasts()
  const [input, setInput] = useState('')

  // 🚀 FEATURE FLAG: Progressive migration to VaultFileHandler
  // Remove streaming upload test toggle
  // const USE_STREAMING_UPLOAD = true // Toggle for testing
  const [showSettings, setShowSettings] = useState(false)
  const [showAttachmentMenu, setShowAttachmentMenu] = useState(false)
  const [showFilePicker, setShowFilePicker] = useState(false)
  const [filePickerMode, setFilePickerMode] = useState<'unified' | 'files' | 'images'>('unified')
  const [attachedFiles, setAttachedFiles] = useState<FileRecord[]>([])
  const [showAutocomplete, setShowAutocomplete] = useState(false)
  const [autocompleteQuery, setAutocompleteQuery] = useState('')
  const [processingFiles, setProcessingFiles] = useState<Set<string>>(new Set())
  const [processingProgress, setProcessingProgress] = useState<Map<string, { progress: number; status: string }>>(new Map())
  const [autocompletePosition, setAutocompletePosition] = useState({ top: 0, left: 0 })
  const [selectedContextId, setSelectedContextId] = useState<string | null>(null)
  const [cursorPosition, setCursorPosition] = useState(0)
  const [isDragOver, setIsDragOver] = useState(false)

  // REMOVED: isDragOver and dragCounter state - no longer needed
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const attachButtonRef = useRef<HTMLButtonElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  const _handleContextChange = (contextId: string | null) => {
    setSelectedContextId(contextId)
    console.log('Context selected for auto-classification:', contextId)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!input.trim() || !currentConversationId || isLoading) return

    const message = input.trim()
    setInput('')



    // Clear attached files after sending
    const filesToSend = [...attachedFiles]
    setAttachedFiles([])

    // Reset textarea height
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto'
    }

    try {
      // TODO: Update sendMessage to handle file attachments and context classification
      await sendMessage(message, currentConversationId, filesToSend, selectedContextId)
    } catch (error) {
      console.error('Error sending message:', error)
      // The error will be handled by the store and displayed in the chat
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    // Don't handle Enter if autocomplete is open (let autocomplete handle it)
    if (showAutocomplete && (e.key === 'Enter' || e.key === 'ArrowUp' || e.key === 'ArrowDown' || e.key === 'Escape')) {
      return
    }

    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSubmit(e)
    }
  }

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto'
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`
    }
  }, [input])

  // Optimized clipboard paste handler for images
  useEffect(() => {
    const handlePaste = async (e: ClipboardEvent) => {
      // Only handle paste if input area is focused
      if (document.activeElement !== textareaRef.current) return

      const items = e.clipboardData?.items
      if (!items) return

      for (let i = 0; i < items.length; i++) {
        const item = items[i]

        if (item.type.startsWith('image/')) {
          e.preventDefault()

          try {
            const file = item.getAsFile()
            if (!file) continue

            // Create a unique filename
            const timestamp = Date.now()
            const extension = file.type.split('/')[1] || 'png'
            const filename = `pasted-image-${timestamp}.${extension}`

            // Convert file to base64 and save to uploads
            const base64String = await fileToBase64(file)

            if (window.electronAPI?.files) {
              // Save the image to the uploads folder
              const tempPath = await window.electronAPI.files.saveContentAsFile(
                base64String,
                filename,
                'Uploads'
              ) as string

              // Index the file (metadata only)
              const fileId = await window.electronAPI.files.indexFile(tempPath)

              if (fileId) {
                // Get the file record and add to attachments
                const files = await window.electronAPI.files.getIndexedFiles()
                const newFile = files.find(f => f.id === fileId)
                if (newFile) {
                  setAttachedFiles(prev => [...prev, newFile])
                }
              }
            }
          } catch (error) {
            console.error('Error processing pasted image:', error)
          }

          break // Only process the first image
        }
      }
    }

    document.addEventListener('paste', handlePaste)
    return () => document.removeEventListener('paste', handlePaste)
  }, [])

  const handleFileSelect = () => {
    console.log('[INPUT-AREA] 📎 Unified file select triggered from AttachmentMenu')
    setFilePickerMode('unified')
    setShowFilePicker(true)
  }

  const handleBrowseFiles = async () => {
    console.log('[INPUT-AREA] 📁 Browse system files triggered')
    // Use electron dialog to browse system files
    if (window.electronAPI?.files?.showOpenDialog) {
      try {
        const result = await window.electronAPI.files.showOpenDialog({
          title: 'Select Files',
          properties: ['openFile', 'multiSelections'],
          filters: [
            { name: 'All Files', extensions: ['*'] },
            { name: 'Documents', extensions: ['pdf', 'doc', 'docx', 'txt', 'md', 'rtf'] },
            { name: 'Images', extensions: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'] },
            { name: 'Spreadsheets', extensions: ['xls', 'xlsx', 'csv'] },
            { name: 'Presentations', extensions: ['ppt', 'pptx'] }
          ]
        })

        if (result && result.filePaths && result.filePaths.length > 0) {
          // Process selected files
          const fileRecords: FileRecord[] = []

          for (const filePath of result.filePaths) {
            // Create file record for each selected file
            const fileName = filePath.split(/[/\\]/).pop() || 'Unknown'
            const fileExtension = fileName.split('.').pop()?.toLowerCase() || ''

            const fileRecord: FileRecord = {
              id: `temp_${Date.now()}_${Math.random()}`,
              filename: fileName,
              filepath: filePath,
              file_type: getFileTypeFromExtension(fileExtension),
              file_size: 0, // Will be determined during processing
              content_hash: '',
              extracted_content: '',
              metadata: JSON.stringify({ source: 'system_browse' }),
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            }

            fileRecords.push(fileRecord)
          }

          // Add to attached files
          setAttachedFiles(prev => [...prev, ...fileRecords])
          console.log('[INPUT-AREA] ✅ Added', fileRecords.length, 'files from system browse')
        }
      } catch (error) {
        console.error('[INPUT-AREA] 💥 Error browsing system files:', error)
      }
    }
  }

  const getFileTypeFromExtension = (extension: string): string => {
    const imageExts = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg']
    const docExts = ['pdf', 'doc', 'docx', 'txt', 'md', 'rtf']

    if (imageExts.includes(extension)) return 'image'
    if (docExts.includes(extension)) return 'document'
    return 'file'
  }

  // 🌊 NEW: Streaming file upload handler
  const handleDroppedFilesStreaming = async (files: File[]) => {
    console.log('🌊 [STREAMING] Files dropped:', files.length)

    try {
      const processedFiles: FileRecord[] = []

      for (const file of files) {
        // Check file type (same validation as legacy)
        const isImage = file.type.startsWith('image/')
        const isPDF = file.type === 'application/pdf'
        const isText = file.type.startsWith('text/')
        const isWord = file.type.includes('word') || file.name.endsWith('.docx')
        const isExcel = file.type.includes('sheet') || file.name.endsWith('.xlsx')
        const isPowerPoint = file.type.includes('presentation') || file.name.endsWith('.pptx')

        if (!isImage && !isPDF && !isText && !isWord && !isExcel && !isPowerPoint) {
          toasts.error(`Unsupported file type: ${file.name}`)
          continue
        }

        // Validate images (same as legacy)
        if (isImage) {
          const validation = validateImageFile(file)
          if (!validation.isValid) {
            toasts.error(`${file.name}: ${validation.error}`)
            continue
          }

          const dimensionValidation = await validateImageDimensions(file)
          if (!dimensionValidation.isValid) {
            toasts.error(`${file.name}: ${dimensionValidation.error}`)
            continue
          }

          if (validation.warnings) {
            validation.warnings.forEach(warning => {
              toasts.error(`${file.name}: ${warning}`)
            })
          }
        }

        // Optimize image if needed (same as legacy)
        let fileToProcess = file
        if (isImage && shouldOptimizeImage(file)) {
          try {
            const optimized = await resizeImage(file, {
              maxWidth: 2048,
              maxHeight: 2048,
              quality: 0.85
            })
            fileToProcess = optimized.file
            toasts.success(`${file.name} optimized: ${Math.round((1 - optimized.newSize / optimized.originalSize) * 100)}% size reduction`)
          } catch (error) {
            console.warn('Image optimization failed, using original:', error)
          }
        }

        // 🌊 NEW: Use VaultFileHandler with streaming support
        try {
          console.log(`🌊 [STREAMING] Uploading ${file.name} (${(file.size / 1024 / 1024).toFixed(2)}MB)`)

          // Use the replaceSharedDropboxService method for compatibility
          const uploadResults = await vaultFileHandler.replaceSharedDropboxService(
            [fileToProcess],
            undefined, // Let service determine destination automatically
            (fileName, progress) => {
              // Real-time progress logging
              console.log(`📊 [STREAMING] ${fileName}: ${progress.percentage.toFixed(1)}% (${progress.totalChunks > 1 ? 'streaming' : 'batch'})`)
            }
          )

          if (uploadResults.length > 0 && uploadResults[0].success && uploadResults[0].filePath) {
            // Index the uploaded file
            const fileId = await window.electronAPI?.files?.indexFile(uploadResults[0].filePath)

            if (fileId) {
              // Get the file record
              const files = await window.electronAPI?.files?.getIndexedFiles()
              const fileRecord = files?.find(f => f.id === fileId)

              if (fileRecord) {
                processedFiles.push(fileRecord)

                const method = uploadResults[0].metadata?.method || 'unknown'
                const processingTime = uploadResults[0].metadata?.processingTime || 0
                const sizeMB = (file.size / 1024 / 1024).toFixed(1)

                console.log(`✅ [STREAMING] ${file.name} uploaded successfully using ${method} method (${processingTime}ms)`)
                console.log('🚨 [DEBUG] About to show success toast:', `✅ "${file.name}" (${sizeMB}MB) uploaded successfully!`)
                toasts.success(`✅ "${file.name}" (${sizeMB}MB) uploaded successfully!`)
              }
            }
          } else {
            const error = uploadResults[0]?.error || 'Unknown error'
            toasts.error(`Failed to upload ${file.name}: ${error}`)
          }
        } catch (error) {
          console.error(`❌ [STREAMING] Error uploading ${file.name}:`, error)

          // Handle large file guidance
          if (error instanceof Error && error.message.startsWith('LARGE_FILE_GUIDANCE:')) {
            const [, sizeGB, destinationType, destinationPath] = error.message.split(':')

            // Show guidance toast for large files
            const folderName = destinationType === 'context' ? 'context vault folder' : 'shared dropbox folder'
            console.log('🚨 [DEBUG] About to show large file toast:', `📁 File too large (${sizeGB}GB) - Please manually copy "${file.name}" to your ${folderName}`)
            toasts.info(`📁 File too large (${sizeGB}GB) - Please manually copy "${file.name}" to your ${folderName}`)

            // Optionally open the folder if path is available
            if (destinationPath && destinationPath !== 'shared-dropbox') {
              console.log(`💡 User can manually open folder: ${destinationPath}`)
            }
          } else if (error instanceof Error && error.message.startsWith('FILE_TOO_LARGE:')) {
            const [, sizeGB, maxSizeGB, destinationType, destinationPath] = error.message.split(':')
            
            // Show error for files exceeding maximum size
            toasts.error(`📁 File "${file.name}" (${sizeGB}GB) exceeds maximum allowed size (${maxSizeGB}GB)`)
          } else {
            toasts.error(`Failed to upload ${file.name}`)
          }
        }
      }

      if (processedFiles.length > 0) {
        setAttachedFiles(prev => [...prev, ...processedFiles])

        const destinationName = selectedContextId
          ? `context vault`
          : 'shared dropbox'

        toasts.success(`🌊 ${processedFiles.length} file(s) uploaded to ${destinationName} using streaming!`)

        // Auto-vectorize dropped files if needed
        for (const file of processedFiles) {
          if (!file.extracted_content) {
            await handleVectorizeFile(file.id)
          }
        }

        // Update conversation title for better readability
        if (processedFiles.length > 0 && currentConversationId) {
          const fileNames = processedFiles.map(f => f.name).join(', ')
          const truncatedNames = fileNames.length > 30 ? fileNames.substring(0, 30) + '...' : fileNames
          const newTitle = `📁 File Upload: ${truncatedNames}`
          
          // Only update if title is significantly different
          if (!currentConversation?.title?.includes(truncatedNames)) {
            try {
              await apiClient.conversations.update(currentConversationId, { title: newTitle })
              console.log('📝 Updated conversation title for file upload:', newTitle)
            } catch (error) {
              console.warn('⚠️ Failed to update conversation title:', error)
            }
          }
        }
      }
    } catch (error) {
      console.error('❌ [STREAMING] Error processing dropped files:', error)
      toasts.error('Error processing dropped files')
    }
  }

  // 📦 LEGACY: Original file upload handler (kept for fallback)
  const handleDroppedFilesLegacy = async (files: File[]) => {
    console.log('📦 [LEGACY] Files dropped:', files.length)

    try {
      const processedFiles: FileRecord[] = []

      for (const file of files) {
        // Check file type
        const isImage = file.type.startsWith('image/')
        const isPDF = file.type === 'application/pdf'
        const isText = file.type.startsWith('text/')
        const isWord = file.type.includes('word') || file.name.endsWith('.docx')
        const isExcel = file.type.includes('sheet') || file.name.endsWith('.xlsx')
        const isPowerPoint = file.type.includes('presentation') || file.name.endsWith('.pptx')

        if (!isImage && !isPDF && !isText && !isWord && !isExcel && !isPowerPoint) {
          toasts.error(`Unsupported file type: ${file.name}`)
          continue
        }

        // Validate images
        if (isImage) {
          const validation = validateImageFile(file)
          if (!validation.isValid) {
            toasts.error(`${file.name}: ${validation.error}`)
            continue
          }

          // Check dimensions
          const dimensionValidation = await validateImageDimensions(file)
          if (!dimensionValidation.isValid) {
            toasts.error(`${file.name}: ${dimensionValidation.error}`)
            continue
          }

          // Show warnings if any
          if (validation.warnings) {
            validation.warnings.forEach(warning => {
              toasts.error(`${file.name}: ${warning}`)
            })
          }
        }

        // Optimize image if needed
        let fileToProcess = file
        if (isImage && shouldOptimizeImage(file)) {
          try {
            const optimized = await resizeImage(file, {
              maxWidth: 2048,
              maxHeight: 2048,
              quality: 0.85
            })
            fileToProcess = optimized.file
            toasts.success(`${file.name} optimized: ${Math.round((1 - optimized.newSize / optimized.originalSize) * 100)}% size reduction`)
          } catch (error) {
            console.warn('Image optimization failed, using original:', error)
          }
        }

        // 📦 LEGACY: Use shared dropbox service for context-aware upload
        const uploadResult = await sharedDropboxService.uploadFile(fileToProcess)

        if (uploadResult.success && uploadResult.fileRecord) {
          // Convert SharedDropboxFile to FileRecord format
          const fileRecord: FileRecord = {
            id: uploadResult.fileRecord.id,
            filename: uploadResult.fileRecord.filename,
            filepath: uploadResult.fileRecord.filepath,
            file_path: uploadResult.fileRecord.filepath, // Legacy compatibility
            file_type: uploadResult.fileRecord.fileType,
            file_size: uploadResult.fileRecord.fileSize,
            content_hash: '', // Not used in new system
            file_hash: '', // Legacy compatibility
            created_at: uploadResult.fileRecord.uploadedAt,
            updated_at: uploadResult.fileRecord.uploadedAt,
            indexed_at: uploadResult.fileRecord.uploadedAt, // Legacy compatibility
            extracted_content: uploadResult.fileRecord.extractedContent || undefined,
            metadata: {
              uploaded_via: 'drag_drop',
              processed: uploadResult.fileRecord.processed
            }
          }

          processedFiles.push(fileRecord)

          // Get upload destination info for user feedback
          const destination = await sharedDropboxService.getUploadDestination()
          const destinationName = destination.type === 'context'
            ? `context vault "${destination.contextName}"`
            : 'shared dropbox'

          console.log(`[Upload] ${file.name} saved to ${destinationName}`)
        } else {
          toasts.error(`Failed to upload ${file.name}: ${uploadResult.error}`)
        }
      }

      if (processedFiles.length > 0) {
        setAttachedFiles(prev => [...prev, ...processedFiles])

        // Show success message with destination info
        const destination = await sharedDropboxService.getUploadDestination()
        const destinationName = destination.type === 'context'
          ? `context vault "${destination.contextName}"`
          : 'shared dropbox'

        toasts.success(`${processedFiles.length} file(s) uploaded to ${destinationName}!`)

        // Auto-vectorize dropped files if needed
        for (const file of processedFiles) {
          if (!file.extracted_content) {
            await handleVectorizeFile(file.id)
          }
        }
      }
    } catch (error) {
      console.error('📦 [LEGACY] Error processing dropped files:', error)
      toasts.error('Error processing dropped files')
    }
  }

  // 🔄 MAIN: Feature flag router for file uploads
  const handleDroppedFiles = async (files: File[]) => {
    // Always use streaming upload if available, fallback to legacy
    if (window.electronAPI?.files) {
      console.log('🌊 [ROUTER] Using streaming upload')
      return await handleDroppedFilesStreaming(files)
    } else {
      console.log('📦 [ROUTER] Using legacy upload')
      return await handleDroppedFilesLegacy(files)
    }
  }

  // 🎯 DRAG & DROP EVENT HANDLERS (Anti-flickering with debounce)
  const dragTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()

    // Clear any pending drag leave timeout
    if (dragTimeoutRef.current) {
      clearTimeout(dragTimeoutRef.current)
      dragTimeoutRef.current = null
    }

    if (!isDragOver) {
      setIsDragOver(true)
      console.log('🎯 [DRAG OVER] Chat input area activated')
    }
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()

    // Use timeout to prevent flickering from rapid enter/leave events
    dragTimeoutRef.current = setTimeout(() => {
      setIsDragOver(false)
      console.log('🚪 [DRAG LEAVE] Chat input area deactivated')
    }, 100) // 100ms debounce
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()

    // Clear any pending timeouts
    if (dragTimeoutRef.current) {
      clearTimeout(dragTimeoutRef.current)
      dragTimeoutRef.current = null
    }

    setIsDragOver(false)

    const files = Array.from(e.dataTransfer.files)
    if (files.length > 0) {
      console.log('🎯 [DROP] Files dropped in chat input area:', files.length)
      handleDroppedFiles(files)
    }
  }

  const handleFilesSelected = async (files: FileRecord[]) => {
    console.log('[INPUT-AREA] 📄 Files selected from FilePicker:', files.length)

    // Set active conversation for intelligence context
    if (currentConversationId) {
      conversationIntelligenceManager.setActiveConversation(currentConversationId)
    }

    // Process files with intelligence-first approach
    const processedFiles: FileRecord[] = []

    for (const file of files) {
      const fileKey = file.filepath || file.id

      try {
        // Add to processing set and show progress
        setProcessingFiles(prev => new Set(prev).add(fileKey))
        setProcessingProgress(prev => new Map(prev).set(fileKey, { progress: 0, status: 'Starting...' }))

        console.log('[INPUT-AREA] 🔄 Processing file with intelligence:', file.filename)

        // Update progress: Checking intelligence
        setProcessingProgress(prev => new Map(prev).set(fileKey, { progress: 20, status: 'Checking intelligence...' }))

        // Get or create intelligence for the file
        if (file.filepath && selectedContextId) {
          // Update progress: Loading intelligence
          setProcessingProgress(prev => new Map(prev).set(fileKey, { progress: 40, status: 'Loading intelligence...' }))

          const intelligence = await unifiedIntelligenceService.getIntelligence(
            {
              filePath: file.filepath,
              vaultPath: selectedContextId,
              fileSize: file.file_size,
              lastModified: file.updated_at
            },
            { includeRawContent: true }
          )

          // Update progress: Attaching to conversation
          setProcessingProgress(prev => new Map(prev).set(fileKey, { progress: 70, status: 'Attaching to conversation...' }))

          // Attach to conversation intelligence context
          if (currentConversationId) {
            await conversationIntelligenceManager.attachFileToConversation(
              file.filepath,
              selectedContextId,
              { includeInHistory: true }
            )
          }

          // Update progress: Finalizing
          setProcessingProgress(prev => new Map(prev).set(fileKey, { progress: 90, status: 'Finalizing...' }))

          // Update file record with extracted content if available
          if (intelligence.extractedContent) {
            file.extracted_content = intelligence.extractedContent
            file.metadata = JSON.stringify({
              ...JSON.parse(file.metadata || '{}'),
              hasIntelligence: true,
              intelligenceSource: intelligence.source,
              processingTime: intelligence.processingTime
            })
          }
        }

        // Update progress: Complete
        setProcessingProgress(prev => new Map(prev).set(fileKey, { progress: 100, status: 'Complete!' }))

        processedFiles.push(file)
        console.log('[INPUT-AREA] ✅ File processed successfully:', file.filename)

      } catch (error) {
        console.error('[INPUT-AREA] 💥 Error processing file:', file.filename, error)

        // Update progress: Error
        setProcessingProgress(prev => new Map(prev).set(fileKey, { progress: 100, status: 'Error - using fallback' }))

        // Still add the file even if processing fails, but try auto-vectorize
        if (!file.extracted_content) {
          handleVectorizeFile(file.id)
        }
        processedFiles.push(file)
      } finally {
        // Remove from processing set after a delay to show completion
        setTimeout(() => {
          setProcessingFiles(prev => {
            const newSet = new Set(prev)
            newSet.delete(fileKey)
            return newSet
          })
          setProcessingProgress(prev => {
            const newMap = new Map(prev)
            newMap.delete(fileKey)
            return newMap
          })
        }, 2000)
      }
    }

    setAttachedFiles(prev => [...prev, ...processedFiles])
    setShowFilePicker(false)

    console.log('[INPUT-AREA] ✅ All files processed and attached, ready for chat')
  };

  const handleVectorizeFile = async (fileId: string) => {
    console.log('Vectorizing file:', fileId)
    try {
      if (window.electronAPI?.files) {
        console.log('Processing file content...')
        const success = await window.electronAPI.files.processFileContent(fileId)
        console.log('Processing result:', success)

        if (success) {
          // Refresh the file data
          const files = await window.electronAPI.files.getIndexedFiles()
          const updatedFile = files.find(f => f.id === fileId)
          console.log('Updated file:', updatedFile)

          if (updatedFile) {
            setAttachedFiles(prev => prev.map(f => f.id === fileId ? updatedFile : f))
            toasts.success(`File "${updatedFile.filename}" has been parsed successfully!`)
          }
        } else {
          toasts.error('Failed to parse file. Please try again.')
        }
      } else {
        console.error('ElectronAPI not available')
        toasts.error('ElectronAPI not available.')
      }
    } catch (error) {
      console.error('Error vectorizing file:', error)
      toasts.error('Error occurred while vectorizing file.')
    }
  }



  // Debounce autocomplete to avoid excessive API calls
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value
    const cursorPos = e.target.selectionStart
    setInput(value)
    setCursorPosition(cursorPos)

    // Clear previous debounce timeout
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current)
    }

    // Check for @ symbol for file autocomplete
    const textBeforeCursor = value.substring(0, cursorPos)
    const atMatch = textBeforeCursor.match(/@([^@\s]*)$/)

    if (atMatch) {
      const query = atMatch[1]

      // Debounce the autocomplete to avoid excessive searches
      debounceTimeoutRef.current = setTimeout(() => {
        setAutocompleteQuery(query)

        // Simple positioning - show above textarea
        if (textareaRef.current) {
          const rect = textareaRef.current.getBoundingClientRect()
          setAutocompletePosition({
            top: rect.top - 250, // Show above the textarea
            left: rect.left
          })
          setShowAutocomplete(true)
        }
      }, 150) // 150ms debounce
    } else {
      setShowAutocomplete(false)
    }
  }

  // Cleanup timeouts on unmount
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current)
      }
      if (dragTimeoutRef.current) {
        clearTimeout(dragTimeoutRef.current)
      }
    }
  }, [])

  // REMOVED: Legacy drag & drop functionality that was interfering with HomePage drop zones
  // This was causing global drag/drop conflicts - HomePage now handles its own drop zones

  const handleFileAutocompleteSelect = (file: FileRecord) => {
    const textBeforeCursor = input.substring(0, cursorPosition)
    const textAfterCursor = input.substring(cursorPosition)
    const atMatch = textBeforeCursor.match(/@([^@\s]*)$/)

    if (atMatch) {
      const beforeAt = textBeforeCursor.substring(0, atMatch.index)
      const newText = beforeAt + `@${file.filename} ` + textAfterCursor
      setInput(newText)

      // Set cursor position after the inserted filename
      const newCursorPos = beforeAt.length + file.filename.length + 2
      setTimeout(() => {
        if (textareaRef.current) {
          textareaRef.current.setSelectionRange(newCursorPos, newCursorPos)
          textareaRef.current.focus()
        }
      }, 0)
    }

    setShowAutocomplete(false)
  }

  if (!currentConversationId) {
    return null
  }

  return (
    <div
      ref={containerRef}
      className="border-t border-tertiary/30 bg-gray-900/40 backdrop-blur-sm relative"
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
    >
      {/* 🎯 Drag & Drop Overlay (Anti-flickering) */}
      {isDragOver && (
        <div className="absolute inset-0 bg-primary/20 border-2 border-dashed border-primary rounded-lg flex items-center justify-center z-50 pointer-events-none">
          <div className="text-center pointer-events-none">
            <div className="text-2xl mb-2">📁</div>
            <div className="text-primary font-medium">Drop files to attach to chat</div>
          </div>
        </div>
      )}

      <form onSubmit={handleSubmit} className="p-4 md:p-6">
        <div className="max-w-4xl mx-auto">
          {/* File Processing Progress */}
          {processingFiles.size > 0 && (
            <div className="mb-4 p-3 bg-neutral-800 border border-neutral-700 rounded-lg">
              <div className="text-sm font-medium text-neutral-200 mb-2">
                Processing Files ({processingFiles.size})
              </div>
              {Array.from(processingFiles).map(fileKey => {
                const progress = processingProgress.get(fileKey)
                return (
                  <div key={fileKey} className="mb-2 last:mb-0">
                    <div className="flex items-center justify-between text-xs text-neutral-400 mb-1">
                      <span className="truncate max-w-[200px]">
                        {fileKey.split(/[/\\]/).pop() || fileKey}
                      </span>
                      <span>{progress?.progress || 0}%</span>
                    </div>
                    <div className="w-full bg-neutral-700 rounded-full h-1.5">
                      <div
                        className="bg-indigo-500 h-1.5 rounded-full transition-all duration-300"
                        style={{ width: `${progress?.progress || 0}%` }}
                      />
                    </div>
                    {progress?.status && (
                      <div className="text-xs text-neutral-500 mt-1">
                        {progress.status}
                      </div>
                    )}
                  </div>
                )
              })}
            </div>
          )}

          {/* File Attachments */}
          <FileAttachments
            attachedFiles={attachedFiles}
            onRemoveFile={(fileId) => {
              setAttachedFiles(prev => prev.filter(f => f.id !== fileId))
            }}
            onVectorizeFile={handleVectorizeFile}
          />

          <div className="flex items-center gap-2">
          {/* Attachment button - moved to left of prompt */}
          <button
            ref={attachButtonRef}
            type="button"
            onClick={() => {
              setShowAttachmentMenu(true)
            }}
            className="flex items-center justify-center w-10 h-10 rounded-lg bg-gray-700 hover:bg-gray-600 transition-colors"
            title="Attach file"
          >
            <FontAwesomeIcon icon={ICONS.plus} className="h-5 w-5 text-gray-300" />
          </button>

          {/* Removed test tube button and any dev-only scripts attached */}

          {/* Removed bottom context vault selector (duplicate) */}

          {/* Input field */}
          <div className="flex-1 relative">
            <textarea
              ref={textareaRef}
              value={input}
              onChange={handleInputChange}
              onKeyDown={handleKeyDown}
              placeholder="Type your message… (Shift+Enter for new line, @filename to reference files)"
              className="u1-textarea w-full pr-20 min-h-[40px] max-h-32 resize-none"
              rows={1}
              disabled={isLoading}
            />

            {/* Character count */}
            {input.length > 0 && (
              <div className="absolute bottom-2 right-16 text-xs text-gray-500">
                {input.length}
              </div>
            )}
          </div>

          {/* Settings button */}
          <button
            type="button"
            onClick={() => setShowSettings(true)}
            className="flex items-center justify-center w-10 h-10 rounded-lg bg-gray-700 hover:bg-gray-600 transition-colors"
            title="Chat settings"
          >
            <FontAwesomeIcon icon={ICONS.cog} className="h-5 w-5 text-gray-300" />
          </button>

          {/* Send button */}
          <button
            type="submit"
            disabled={!input.trim() || isLoading}
            className={`
              flex items-center justify-center w-10 h-10 rounded-lg transition-colors
              ${input.trim() && !isLoading
                ? 'bg-primary hover:bg-primary/80 text-gray-900'
                : 'bg-gray-800 text-gray-500 cursor-not-allowed'
              }
            `}
            title="Send message"
          >
            {isLoading ? (
              <FontAwesomeIcon icon={ICONS.spinner} className="h-5 w-5 animate-spin" />
            ) : (
              <FontAwesomeIcon icon={ICONS.paperPlane} className="h-5 w-5" />
            )}
          </button>
          </div>
        </div>

        {/* Debug indicator for chat-notes storage */}
        <div className="max-w-4xl mx-auto mt-2 mb-1">
          <div className="text-xs text-green-400 font-mono bg-green-900/20 border border-green-500/30 rounded px-2 py-1 inline-block">
            💾 Chat-notes will be stored in vault intelligence system
          </div>
        </div>

        {/* Model selector and options */}
        <div className="max-w-4xl mx-auto mt-2 flex items-center justify-between text-xs text-neutral-500">
          <div className="flex items-center gap-4">
            <span>Model: {settings.selectedModel ?
              models.find(m => m.id === settings.selectedModel)?.name || settings.selectedModel
              : 'No model selected'}</span>
            <span>•</span>
            <span>Temp: {settings.temperature?.toFixed(1) || '0.7'}</span>
            <span>•</span>
            <span>Max: {settings.maxTokens?.toLocaleString() || '4K'}</span>
            {settings.topP && (
              <>
                <span>•</span>
                <span>Top-P: {settings.topP.toFixed(2)}</span>
              </>
            )}
          </div>

          <div className="flex items-center gap-2">
            <span>Shift+Enter for new line</span>
          </div>
        </div>
      </form>

      {/* Attachment Menu */}
      <AttachmentMenu
        isOpen={showAttachmentMenu}
        onClose={() => setShowAttachmentMenu(false)}
        onFileSelect={handleFileSelect}
        onBrowseFiles={handleBrowseFiles}
        anchorRef={attachButtonRef as React.RefObject<HTMLElement>}
      />

      {/* File Picker */}
      <FilePicker
        isOpen={showFilePicker}
        onClose={() => setShowFilePicker(false)}
        onFileSelect={handleFilesSelected}
        mode={filePickerMode}
      />

      {/* File Autocomplete */}
      <FileAutocomplete
        isOpen={showAutocomplete}
        query={autocompleteQuery}
        position={autocompletePosition}
        onSelect={handleFileAutocompleteSelect}
        onClose={() => setShowAutocomplete(false)}
      />

      {/* Chat Settings Drawer */}
      <ChatSettingsDrawer
        isOpen={showSettings}
        onClose={() => setShowSettings(false)}
      />


    </div>
  )
}

export default InputArea
