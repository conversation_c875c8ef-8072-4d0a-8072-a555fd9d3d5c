/**
 * Simple Vault Module
 * Direct replacement for the current vault registry implementation
 * No external dependencies - works with existing system
 */

import { BaseAPIModule, ModuleConfig, ModuleDependency } from '../core/BaseAPIModule'

export class SimpleVaultModule extends BaseAPIModule {
  readonly name = 'vault'
  readonly version = '1.0.0'
  readonly description = 'Simple vault operations - direct replacement for current implementation'
  readonly dependencies: ModuleDependency[] = [
    { name: 'database', optional: false }
  ]

  private db: any
  private fs: any
  private path: any
  private os: any

  protected async onInitialize(): Promise<void> {
    // Import required modules dynamically
    this.fs = await import('fs')
    this.path = await import('path')
    this.os = await import('os')
    
    // Get database from the registry (will be injected)
    this.db = this.getDependency('database')
    
    this.log('info', 'Simple Vault Module initialized successfully')
  }

  async registerEndpoints(): Promise<void> {
    this.log('info', 'Registering simple vault endpoints...')

    // Direct replacement for getVaultRegistry
    this.registerEndpoint('vault', 'getVaultRegistry',
      async () => {
        try {
          console.log('🔍 [SIMPLE-VAULT] === GET VAULT REGISTRY START ===')
          
          // First try to get saved vault root path from database
          let vaultRoot = this.db.getSetting('vault-root-path')
          console.log('🔍 [SIMPLE-VAULT] Database setting "vault-root-path":', vaultRoot)

          // Fall back to default if no saved path
          if (!vaultRoot) {
            vaultRoot = this.path.join(this.os.homedir(), 'Documents', 'ChatLo_Vaults')
            console.log('🔍 [SIMPLE-VAULT] ⚠️ No database setting found, using default vault root:', vaultRoot)
            
            // Persist default for consistency across services
            try { 
              this.db.setSetting('vault-root-path', vaultRoot) 
              console.log('🔍 [SIMPLE-VAULT] ✅ Default vault root saved to database')
            } catch (dbError) {
              console.error('🔍 [SIMPLE-VAULT] ❌ Failed to save default vault root to database:', dbError)
            }
          }

          console.log('🔍 [SIMPLE-VAULT] Final vault root path:', vaultRoot)

          const registryPath = this.path.join(vaultRoot, '.chatlo', 'vault-registry.json')
          console.log('🔍 [SIMPLE-VAULT] Registry file path:', registryPath)

          // Check if registry file exists
          const registryExists = await this.fs.promises.access(registryPath).then(() => true).catch(() => false)
          console.log('🔍 [SIMPLE-VAULT] Registry file exists:', registryExists)

          if (!registryExists) {
            console.log('🔍 [SIMPLE-VAULT] ⚠️ Registry file not found, returning error')
            console.log('🔍 [SIMPLE-VAULT] === GET VAULT REGISTRY END (NO FILE) ===')
            return { success: false, error: 'Registry file not found', data: null }
          }

          // Read and parse registry file
          console.log('🔍 [SIMPLE-VAULT] Reading registry file...')
          const content = await this.fs.promises.readFile(registryPath, 'utf8')
          console.log('🔍 [SIMPLE-VAULT] Registry file size:', content.length, 'characters')
          
          const registry = JSON.parse(content)
          console.log('🔍 [SIMPLE-VAULT] Registry parsed successfully')
          console.log('🔍 [SIMPLE-VAULT] Registry structure:', {
            hasVaultRoot: !!registry.vaultRoot,
            vaultRoot: registry.vaultRoot,
            vaultsCount: registry.vaults?.length || 0,
            hasContexts: registry.vaults?.some((v: any) => v.contexts) || false
          })
          
          if (registry.vaults && Array.isArray(registry.vaults)) {
            registry.vaults.forEach((vault: any, index: number) => {
              console.log(`🔍 [SIMPLE-VAULT] Vault ${index + 1} in registry:`, {
                name: vault.name,
                path: vault.path,
                pathType: typeof vault.path,
                contextsCount: vault.contexts?.length || 0
              })
            })
          }
          
          console.log('🔍 [SIMPLE-VAULT] === GET VAULT REGISTRY END (SUCCESS) ===')
          return { success: true, data: registry }
        } catch (error: any) {
          console.error('🔍 [SIMPLE-VAULT] 💥 Error getting vault registry:', error)
          console.log('🔍 [SIMPLE-VAULT] === GET VAULT REGISTRY END (ERROR) ===')
          return { success: false, error: error.message, data: null }
        }
      },
      { description: 'Get vault registry configuration (SimpleVaultModule)' }
    )

    // Direct replacement for saveVaultRegistry
    this.registerEndpoint('vault', 'saveVaultRegistry',
      async (registry: any) => {
        try {
          console.log('🔍 [SIMPLE-VAULT] === SAVE VAULT REGISTRY START ===')
          
          if (!registry || typeof registry !== 'object') {
            throw new Error('Invalid registry object')
          }

          // Get vault root path
          let vaultRoot = this.db.getSetting('vault-root-path')
          if (!vaultRoot) {
            vaultRoot = this.path.join(this.os.homedir(), 'Documents', 'ChatLo_Vaults')
            this.db.setSetting('vault-root-path', vaultRoot)
          }

          // Ensure .chatlo directory exists
          const chatloDir = this.path.join(vaultRoot, '.chatlo')
          await this.fs.promises.mkdir(chatloDir, { recursive: true })

          // Save registry file
          const registryPath = this.path.join(chatloDir, 'vault-registry.json')
          const content = JSON.stringify(registry, null, 2)
          await this.fs.promises.writeFile(registryPath, content, 'utf8')

          console.log('🔍 [SIMPLE-VAULT] ✅ Registry saved successfully')
          console.log('🔍 [SIMPLE-VAULT] === SAVE VAULT REGISTRY END (SUCCESS) ===')
          return { success: true, message: 'Registry saved successfully' }
        } catch (error: any) {
          console.error('🔍 [SIMPLE-VAULT] 💥 Error saving vault registry:', error)
          console.log('🔍 [SIMPLE-VAULT] === SAVE VAULT REGISTRY END (ERROR) ===')
          return { success: false, error: error.message }
        }
      },
      { description: 'Save vault registry configuration (SimpleVaultModule)' }
    )

    this.log('info', `Registered ${this.endpoints.size} simple vault endpoints`)
  }
}
