import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { ArrowLeft, Search, Calendar, MessageSquare, Pin, Trash2 } from '../components/Icons';
import { useAppStore } from '../store';


interface ConversationStats {
  messageCount: number;
  lastActivity: string;
  isPinned: boolean;
  firstLLMReply: string;
  hasArtifacts: boolean;
}

const HistoryPage: React.FC = () => {
  const navigate = useNavigate();
  const { conversations, loadConversations, deleteConversation } = useAppStore();
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'recent' | 'oldest' | 'title' | 'messages'>('recent');
  const [filterBy, setFilterBy] = useState<'all' | 'pinned' | 'recent'>('all');
  const [conversationStats, setConversationStats] = useState<Record<string, ConversationStats>>({});
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadConversations();
  }, [loadConversations]);

  useEffect(() => {
    const loadStats = async () => {
      setIsLoading(true);
      const stats: Record<string, ConversationStats> = {};

      for (const conversation of conversations) {
        try {
          const messagesResponse = await window.electronAPI.db.getMessages(conversation.id);
          const artifactsResponse = await window.electronAPI.db.getConversationArtifacts(conversation.id);
          
          // Handle both direct array response and wrapped response for backward compatibility
          const messages = Array.isArray(messagesResponse) ? messagesResponse : (messagesResponse?.success ? messagesResponse.data || [] : [])
          const artifacts = Array.isArray(artifactsResponse) ? artifactsResponse : (artifactsResponse?.success ? artifactsResponse.data || [] : [])

          // Find first assistant message
          const firstLLMMessage = messages.find(m => m.role === 'assistant');
          const firstLLMReply = firstLLMMessage
            ? firstLLMMessage.content.split('\n').slice(0, 2).join('\n').substring(0, 120) + (firstLLMMessage.content.length > 120 ? '...' : '')
            : '';

          stats[conversation.id] = {
            messageCount: messages.length,
            lastActivity: conversation.updated_at,
            isPinned: conversation.is_pinned === 1,
            firstLLMReply,
            hasArtifacts: artifacts.length > 0
          };
        } catch (error) {
          console.error('Failed to load stats for conversation:', conversation.id, error);
          stats[conversation.id] = {
            messageCount: 0,
            lastActivity: conversation.updated_at,
            isPinned: conversation.is_pinned === 1,
            firstLLMReply: '',
            hasArtifacts: false
          };
        }
      }

      setConversationStats(stats);
      setIsLoading(false);
    };

    if (conversations.length > 0) {
      loadStats();
    } else {
      setIsLoading(false);
    }
  }, [conversations]);

  const filteredAndSortedConversations = React.useMemo(() => {
    let filtered = conversations.filter(conversation => {
      const matchesSearch = conversation.title.toLowerCase().includes(searchTerm.toLowerCase());

      switch (filterBy) {
        case 'pinned':
          return matchesSearch && conversation.is_pinned === 1;
        case 'recent':
          const oneWeekAgo = new Date();
          oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
          return matchesSearch && new Date(conversation.updated_at) > oneWeekAgo;
        default:
          return matchesSearch;
      }
    });

    return filtered.sort((a, b) => {
      switch (sortBy) {
        case 'recent':
          return new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime();
        case 'oldest':
          return new Date(a.updated_at).getTime() - new Date(b.updated_at).getTime();
        case 'title':
          return a.title.localeCompare(b.title);
        case 'messages':
          const aCount = conversationStats[a.id]?.messageCount || 0;
          const bCount = conversationStats[b.id]?.messageCount || 0;
          return bCount - aCount;
        default:
          return 0;
      }
    });
  }, [conversations, searchTerm, sortBy, filterBy, conversationStats]);

  const handleConversationClick = (conversationId: string) => {
    navigate(`/?conversation=${conversationId}`);
  };

  const handleDeleteConversation = async (e: React.MouseEvent, conversationId: string) => {
    e.stopPropagation();
    if (window.confirm('Are you sure you want to delete this conversation? This action cannot be undone.')) {
      try {
        await deleteConversation(conversationId);
      } catch (error) {
        console.error('Failed to delete conversation:', error);
      }
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return 'Today';
    if (diffDays === 2) return 'Yesterday';
    if (diffDays <= 7) return `${diffDays - 1} days ago`;

    return date.toLocaleDateString();
  };

  return (
    <div className="flex-1 flex flex-col h-full bg-gray-900 text-supplement1">
      <header className="flex-shrink-0 h-16 flex items-center gap-3 px-4 md:px-8 border-b border-tertiary bg-gray-800/60 backdrop-blur-lg">
        <Link to="/" className="text-gray-400 hover:text-supplement1 transition-colors">
          <ArrowLeft className="h-5 w-5" />
        </Link>
        <h2 className="text-lg font-semibold tracking-tight leading-none text-supplement1">Conversation History</h2>
        <div className="ml-auto text-sm text-gray-400">
          {filteredAndSortedConversations.length} of {conversations.length} conversations
        </div>
      </header>

      <main className="flex-1 overflow-y-auto px-4 md:px-8 py-6 min-h-0">
        <div className="max-w-6xl mx-auto space-y-6">
          {/* Search and Filter Controls */}
          <div className="flex flex-col sm:flex-row gap-4">
            {/* Search */}
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search conversations..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="u1-input-search w-full pl-10 pr-4 py-2"
              />
            </div>

            {/* Sort By */}
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as any)}
              className="u1-input-field px-3 py-2"
            >
              <option value="recent">Most Recent</option>
              <option value="oldest">Oldest First</option>
              <option value="title">Title A-Z</option>
              <option value="messages">Most Messages</option>
            </select>

            {/* Filter By */}
            <select
              value={filterBy}
              onChange={(e) => setFilterBy(e.target.value as any)}
              className="u1-input-field px-3 py-2"
            >
              <option value="all">All Conversations</option>
              <option value="pinned">Pinned Only</option>
              <option value="recent">Recent (7 days)</option>
            </select>
          </div>

          {/* Conversations List */}
          {isLoading ? (
            <div className="flex items-center justify-center py-12">
              <div className="text-gray-400">Loading conversations...</div>
            </div>
          ) : filteredAndSortedConversations.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-12 text-gray-400">
              <MessageSquare className="h-12 w-12 mb-4 opacity-50" />
              <h3 className="text-lg font-medium mb-2 text-supplement1">No conversations found</h3>
              <p className="text-sm">
                {searchTerm ? 'Try adjusting your search terms' : 'Start a new conversation to see it here'}
              </p>
            </div>
          ) : (
            <div className="grid gap-4">
              {filteredAndSortedConversations.map((conversation) => {
                const stats = conversationStats[conversation.id];
                return (
                  <div
                    key={conversation.id}
                    onClick={() => handleConversationClick(conversation.id)}
                    className={`group cursor-pointer transition-all duration-200 ${stats?.hasArtifacts ? '-mx-4 md:-mx-8' : ''}`}
                  >
                    <div
                      className={`u1-card bg-gray-800/50 border border-gray-700 group-hover:bg-gray-700/50 transition-colors ${stats?.hasArtifacts ? 'px-4 md:px-8' : ''}`}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-2">
                            <h3 className="font-medium text-supplement1 truncate group-hover:text-white transition-colors">
                              {conversation.title}
                            </h3>
                            {stats?.isPinned && (
                              <Pin className="h-4 w-4 text-yellow-400 flex-shrink-0" />
                            )}
                            {stats?.hasArtifacts && (
                              <Pin className="h-4 w-4 text-primary flex-shrink-0" />
                            )}
                          </div>

                          {/* LLM Reply Preview */}
                          {stats?.firstLLMReply && (
                            <div className="mb-3 text-sm text-gray-300 bg-gray-900/50 rounded p-2 border-l-2 border-primary/30">
                              <div className="text-xs text-gray-500 mb-1">AI Reply:</div>
                              <div className="whitespace-pre-wrap">{stats.firstLLMReply}</div>
                            </div>
                          )}

                          <div className="flex items-center gap-4 text-sm text-gray-400">
                            <div className="flex items-center gap-1">
                              <MessageSquare className="h-4 w-4" />
                              <span>{stats?.messageCount || 0} messages</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <Calendar className="h-4 w-4" />
                              <span>{formatDate(conversation.updated_at)}</span>
                            </div>
                          </div>
                        </div>

                        <div className="flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
                          <button
                            onClick={(e) => handleDeleteConversation(e, conversation.id)}
                            className="p-2 text-gray-400 hover:text-secondary hover:bg-secondary/10 rounded transition-colors"
                            title="Delete conversation"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </main>
    </div>
  );
};

export default HistoryPage;
