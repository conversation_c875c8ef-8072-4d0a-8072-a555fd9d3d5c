import React, { useState, useEffect, useRef } from 'react'
import { FileText, Image, File } from './Icons'
import { FileRecord } from '../types'

interface FileAutocompleteProps {
  isOpen: boolean
  query: string
  position: { top: number, left: number }
  onSelect: (file: FileRecord) => void
  onClose: () => void
}

const FileAutocomplete: React.FC<FileAutocompleteProps> = ({
  isOpen,
  query,
  position,
  onSelect,
  onClose
}) => {
  const [filteredFiles, setFilteredFiles] = useState<FileRecord[]>([])
  const [selectedIndex, setSelectedIndex] = useState(0)
  const [isLoading, setIsLoading] = useState(false)
  const menuRef = useRef<HTMLDivElement>(null)

  // Only load and filter files when the component opens or query changes
  useEffect(() => {
    if (isOpen) {
      searchFiles()
    }
  }, [isOpen, query])

  useEffect(() => {
    setSelectedIndex(0)
  }, [filteredFiles])

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!isOpen) return

      switch (event.key) {
        case 'ArrowDown':
          event.preventDefault()
          setSelectedIndex(prev => 
            prev < filteredFiles.length - 1 ? prev + 1 : 0
          )
          break
        case 'ArrowUp':
          event.preventDefault()
          setSelectedIndex(prev => 
            prev > 0 ? prev - 1 : filteredFiles.length - 1
          )
          break
        case 'Enter':
          event.preventDefault()
          if (filteredFiles[selectedIndex]) {
            onSelect(filteredFiles[selectedIndex])
          }
          break
        case 'Escape':
          event.preventDefault()
          onClose()
          break
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [isOpen, filteredFiles, selectedIndex, onSelect, onClose])

  const searchFiles = async () => {
    setIsLoading(true)
    try {
      if (window.electronAPI?.files) {
        // Use optimized search that handles both empty and filled queries
        const response = await window.electronAPI.files.searchFiles(query, 8)
        
        // Handle new modular API response format: { success: true, results: [...] }
        if (response.success && response.results) {
          setFilteredFiles(response.results)
        } else {
          console.warn('searchFiles returned unsuccessful response:', response)
          setFilteredFiles([])
        }
      }
    } catch (error) {
      console.error('Error searching files:', error)
      setFilteredFiles([])
    } finally {
      setIsLoading(false)
    }
  }

  const getFileIcon = (fileType: string) => {
    switch (fileType) {
      case 'image':
        return <Image className="h-4 w-4 text-blue-400" />
      case 'pdf':
        return <FileText className="h-4 w-4 text-red-400" />
      case 'word':
        return <FileText className="h-4 w-4 text-blue-600" />
      case 'excel':
        return <FileText className="h-4 w-4 text-green-600" />
      case 'powerpoint':
        return <FileText className="h-4 w-4 text-orange-600" />
      case 'text':
      case 'markdown':
        return <FileText className="h-4 w-4 text-neutral-400" />
      default:
        return <File className="h-4 w-4 text-neutral-400" />
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
  }

  if (!isOpen) return null

  return (
    <div
      ref={menuRef}
      className="fixed z-50 bg-neutral-800 border border-neutral-700 rounded-lg shadow-lg backdrop-blur-lg min-w-[300px] max-w-[400px]"
      style={{
        top: position.top,
        left: position.left,
      }}
    >
      {isLoading ? (
        <div className="p-4 text-center text-neutral-400 text-sm">
          Loading files...
        </div>
      ) : filteredFiles.length === 0 ? (
        <div className="p-4 text-center text-neutral-400 text-sm">
          {query.trim() ? 'No files found' : 'No files available'}
        </div>
      ) : (
        <div className="py-2">
          <div className="px-3 py-1 text-xs text-neutral-500 border-b border-neutral-700">
            Files ({filteredFiles.length})
          </div>
          {filteredFiles.map((file, index) => (
            <button
              key={file.id}
              onClick={() => onSelect(file)}
              className={`
                w-full flex items-center gap-3 px-3 py-2 text-left hover:bg-neutral-700 transition-colors
                ${index === selectedIndex ? 'bg-neutral-700' : ''}
              `}
            >
              {getFileIcon(file.file_type)}
              <div className="flex-1 min-w-0">
                <div className="text-sm text-neutral-200 truncate">
                  {file.filename}
                </div>
                <div className="text-xs text-neutral-500 flex items-center gap-2">
                  <span>{formatFileSize(file.file_size)}</span>
                  <span>•</span>
                  <span className="capitalize">{file.file_type}</span>
                </div>
              </div>
            </button>
          ))}
          
          {filteredFiles.length === 10 && (
            <div className="px-3 py-1 text-xs text-neutral-500 border-t border-neutral-700">
              Showing first 10 results
            </div>
          )}
        </div>
      )}
    </div>
  )
}

export default FileAutocomplete
